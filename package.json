{"name": "DMS_Application", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/checkbox": "^0.5.20", "@react-native-reusables/cli": "^0.5.4", "@react-native/new-app-screen": "0.81.4", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "@reduxjs/toolkit": "^2.9.0", "add": "^2.0.6", "alertify": "^0.3.0", "react": "19.1.0", "react-native": "0.81.4", "react-native-date-picker": "^5.0.13", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "^2.28.0", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "twrnc": "^4.9.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "20.0.0", "@react-native-community/cli-platform-android": "20.0.0", "@react-native-community/cli-platform-ios": "20.0.0", "@react-native/babel-preset": "0.81.4", "@react-native/eslint-config": "0.81.4", "@react-native/metro-config": "0.81.4", "@react-native/typescript-config": "0.81.4", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "^5.8.3"}, "engines": {"node": ">=20"}}