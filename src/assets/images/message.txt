<script>
  (function ensureViewport() {
    try {
      // If there’s already a viewport meta, don’t touch it
      if (document.querySelector('meta[name="viewport"]')) return;

      // If <head> exists, inject immediately; otherwise wait for DOM ready
      function addMeta() {
        if (!document.head) return;
        var m = document.createElement('meta');
        m.name = 'viewport';
        m.content = 'width=device-width, initial-scale=1.0, viewport-fit=cover';
        document.head.appendChild(m);
      }
      if (document.head) addMeta();
      else {
        document.addEventListener('readystatechange', function onr() {
          if (document.readyState === 'interactive' || document.readyState === 'complete') {
            document.removeEventListener('readystatechange', onr);
            addMeta();
          }
        });
      }
    } catch (e) { /* ignore */ }
  })();
</script>


<iframe id="hop-widget" style="width:100%;min-height:900px;border:0;overflow:hidden" loading="lazy"
  referrerpolicy="strict-origin-when-cross-origin"
  sandbox="allow-scripts allow-forms allow-same-origin allow-popups allow-popups-to-escape-sandbox allow-top-navigation-by-user-activation"
  srcdoc='<!doctype html><html><head><meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><style>html,body{margin:0;padding:0;background:transparent}</style></head><body>
<div id="hop-widget-root"></div>
<script>
(function(){
  var W="6865223b0f80599b7591383f";             // <-- your widgetId
  var ORIGIN="https://dashboard.hopwellness.ai";     // <-- your host
  var PREFIX="hw_"+W+"_";
  var root=document.getElementById("hop-widget-root");

  // Auto-resize -> parent
  function postSize(){
    var h=Math.max(document.documentElement.scrollHeight,document.body.scrollHeight,document.documentElement.offsetHeight);
    parent.postMessage({type:"hop:resize",widgetId:W,height:h},"*");
  }
  new ResizeObserver(postSize).observe(document.documentElement);
  window.addEventListener("load",postSize);
  document.addEventListener("readystatechange",postSize);

  // Widget -> Parent: filters
  root.addEventListener("hop-filter-change",function(e){
    var d=e.detail||{}; parent.postMessage({type:"hop:filters",widgetId:W,mode:d.mode||"live",filters:d.filters||{}}, "*");
  });

  // Parent -> Widget: set filters
  window.addEventListener("message",function(e){
    var m=e.data||{}; if(m.type==="hop:setFilters"&&m.widgetId===W){
      root.dispatchEvent(new CustomEvent("hop-set-filters",{detail:m.filters||{}}));
    }
  });

  // Load your existing embed.js (it will pull widget.css + render.js)
  var s=document.createElement("script");
  s.src=ORIGIN+"/widgets/embed.js?widgetId="+encodeURIComponent(W)+"&origin="+encodeURIComponent(ORIGIN);
  s.defer=true; document.body.appendChild(s);
})();
</script>
</body></html>' onload="(function(IF){
    var W='67a5e32e1bbdd61401b5e36c', P='hw_'+W+'_', O='https://dashboard.hopwellness.ai';

    // Receive messages from iframe (resize + filters)
    function onMsg(e){
      if(e.origin!==O) return;
      var m=e.data||{}; if(m.widgetId!==W) return;

      if(m.type==='hop:resize' && m.height){ IF.style.height=m.height+'px'; }

      if(m.type==='hop:filters'){
        var u=new URL(location.href);
        // clear old params for this widget
        u.searchParams.forEach(function(_,k){ if(k.startsWith(P)) u.searchParams.delete(k); });
        // set new ones
        var f=m.filters||{};
        Object.keys(f).forEach(function(k){ var v=f[k]; if(v!=null&&String(v).trim()!=='') u.searchParams.set(P+k,String(v)); });
        history[(m.mode==='apply')?'pushState':'replaceState']({filters:f},'',u);
      }
    }
    window.addEventListener('message', onMsg);

    // Seed current parent filters into the iframe
    function seed(){
      var q=new URLSearchParams(location.search), f={};
      q.forEach(function(v,k){ if(k.startsWith(P)) f[k.slice(P.length)]=v; });
      IF.contentWindow && IF.contentWindow.postMessage({type:'hop:setFilters',widgetId:W,filters:f}, O);
    }
    seed(); window.addEventListener('popstate', seed);
  })(this)">
</iframe>