// MyModal.js
import React from 'react';
import {
  Modal,
  View,
  Text,
  Pressable,
  ScrollView,
  Image,
  TouchableOpacity,
} from 'react-native';
import tw from 'twrnc';
import DealerCodeTable from '../components/dealer-code-table';

const SelectDealerCodeModal = ({
  visible,
  onClose,
  dealerCodes,
  onDealerSelect,
}) => {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View
        style={tw`items-center justify-center flex-1 bg-black bg-opacity-50`}
      >
        <View style={tw`w-[92%]   pt-2 bg-white rounded-lg`}>
          <View
            style={tw`flex flex-row items-center justify-between px-5 mb-4`}
          >
            <Text style={tw`text-lg font-semibold `}>Select Dealer Code</Text>
            <TouchableOpacity onPress={onClose}>
              <Image
                source={require('../assets/images/cross.png')}
                style={tw`w-3 h-3`}
              />
            </TouchableOpacity>
          </View>
          {/* --------------------------table------------------------- */}
          <ScrollView horizontal style={tw`px-2`}>
            <View style={tw`w-[550px]`}>
              <DealerCodeTable
                data={dealerCodes || []}
                onDealerSelect={onDealerSelect}
                onClose={onClose}
              />
            </View>
          </ScrollView>

          {/* <Pressable
            style={tw`px-4 py-2 bg-blue-500 rounded`}
            onPress={onClose}
          >
            <Text style={tw`text-center text-white`}>Close Modal</Text>
          </Pressable> */}
        </View>
      </View>
    </Modal>
  );
};

export default SelectDealerCodeModal;
