// MyModal.js
import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  Pressable,
  ScrollView,
  Image,
  TouchableOpacity,
  // CheckBox,
} from 'react-native';
import tw from 'twrnc';
import CheckBox from '@react-native-community/checkbox';

const OrderCard = [
  {
    id: '1',
  },
  {
    id: '2',
  },
  {
    id: '3',
  },
  {
    id: '4',
  },
];

const AddressListModal = ({ visible, onClose }) => {
  const [selectedId, setSelectedId] = useState(null);
  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View
        style={tw`items-center justify-center flex-1 bg-black bg-opacity-50`}
      >
        <View style={tw`w-[92%]  h-[95%] pt-2 bg-white rounded-lg`}>
          <View
            style={tw`flex flex-row items-center  justify-between px-5 mb-4`}
          >
            <Text style={tw`text-lg font-semibold `}>Address List</Text>
            <TouchableOpacity onPress={onClose}>
              <Image
                source={require('../assets/images/cross.png')}
                style={tw`w-3 h-3`}
              />
            </TouchableOpacity>
          </View>
          {/* --------------------------table------------------------- */}
          <ScrollView style={tw`px-2`}>
            <View style={tw``}>
              {OrderCard.map(item => {
                return (
                  <View
                    key={item.id}
                    style={tw`px-5 py-3 pb-5 mt-3 bg-[#F3F8FC] rounded-lg mx-3`}
                  >
                    <View
                      style={tw`flex-row items-center pb-2 justify-start gap-3`}
                    >
                      <CheckBox
                        value={selectedId === item.id}
                        onValueChange={() =>
                          setSelectedId(prev =>
                            prev === item.id ? null : item.id,
                          )
                        }
                        tintColors={{ true: '#C21616', false: '#9CA3AF' }}
                      />

                      <Text style={tw`text-sm font-semibold text-black`}>
                        Shipping Address
                      </Text>
                    </View>

                    <View>
                      <View
                        style={tw`flex-row border-b border-[#E4E4E4] pb-1 pt-2 items-center justify-between`}
                      >
                        <Text
                          style={tw`text-xs text-black font-semibold font-semibold`}
                        >
                          Name
                        </Text>
                        <View style={tw`w-[60%] `}>
                          <Text style={tw`text-[#000] text-right text-xs`}>
                            Sunrise Sales India Pvt. Ltd.
                          </Text>
                        </View>
                      </View>
                      <View
                        style={tw`flex-row border-b border-[#E4E4E4] pb-1 pt-5 items-center justify-between`}
                      >
                        <Text style={tw`text-xs text-black font-semibold`}>
                          Address
                        </Text>
                        <View style={tw`w-[60%] flex justify-end flex-row `}>
                          <Text style={tw`text-[#000]  text-xs`}>
                            C 109, GALI NO. 1, NAGAR
                          </Text>
                        </View>
                      </View>
                      <View
                        style={tw`flex-row border-b border-[#E4E4E4] pb-1 pt-5 items-center justify-between`}
                      >
                        <Text style={tw`text-xs text-black font-semibold`}>
                          City
                        </Text>
                        <Text style={tw`text-[#000] text-xs`}>Gurugram</Text>
                      </View>
                      <View
                        style={tw`flex-row border-b border-[#E4E4E4] pb-1 pt-5 items-center justify-between`}
                      >
                        <Text style={tw`text-xs text-black font-semibold`}>
                          Pincode
                        </Text>
                        <Text style={tw`text-[#000] text-xs`}>210001</Text>
                      </View>
                      <View
                        style={tw`flex-row border-b border-[#E4E4E4] pb-1 pt-5 items-center justify-between`}
                      >
                        <Text style={tw`text-xs text-black font-semibold`}>
                          State
                        </Text>
                        <Text style={tw`text-[#000] text-xs`}>Haryana</Text>
                      </View>
                    </View>
                  </View>
                );
              })}
            </View>
            <View
              style={tw` py-8 flex flex-row items-center justify-center w-full`}
            >
              {/* Button */}
              <Pressable
                onPress={onClose}
                style={tw`px-4 py-2 bg-[#B91C1C] rounded-lg w-[40%]`}
              >
                <Text style={tw`text-center text-white `}>Save & Next</Text>
              </Pressable>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

export default AddressListModal;
