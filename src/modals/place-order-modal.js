// MyModal.js
import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  Image,
  TouchableOpacity,
  Pressable,
  Button,
  TextInput,
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import tw from 'twrnc';
import { Dropdown } from 'react-native-element-dropdown';
// import { Textarea } from '@/components/ui/textarea';
import { useNavigation } from '@react-navigation/native';

const OrderType = [
  { label: 'Blanket Order', id: 1 },
  { label: 'Normal Order', id: 2 },
];

const Data = [{ label: 'Private' }, { label: 'Government' }];

const Data2 = [
  { label: 'Hospital' },
  { label: 'Power Plant' },
  { label: 'Mall' },
  { label: 'OEM' },
];

const PlaceOrderModal = ({ visible, onClose }) => {
  const [selectedId, setSelectedId] = useState(null);
  const [date, setDate] = useState(new Date());
  const [open, setOpen] = useState(false);
  const navigation = useNavigation();

  const handlePlaceOrder = () => {
    navigation.navigate('Order_listing');
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
      // style={tw`pb-20`}
    >
      <View
        style={tw`items-center justify-center flex-1 bg-black bg-opacity-50 `}
      >
        <View style={tw`w-[92%]   pt-2 bg-white pb-10 rounded-lg`}>
          <View style={tw`flex flex-row items-center justify-end px-5 pt-1`}>
            <TouchableOpacity onPress={onClose}>
              <Image
                source={require('../assets/images/cross.png')}
                style={tw`w-3 h-3`}
              />
            </TouchableOpacity>
          </View>
          {/* --------------------------content------------------------- */}
          <Text style={tw`text-lg font-semibold text-center`}>
            {' '}
            You are placing an order request
          </Text>
          <Text style={tw`px-5 pt-1 pb-3 text-xs text-center`}>
            Soon after placing this request, you will receive an order
            confirmation link on
            <Text style={tw`font-semibold`}> <EMAIL> </Text>
            with the shipping cost from ALP.
          </Text>

          {/* --------------------------radio button------------------------- */}

          <Text style={tw`px-5 pb-2 text-base font-semibold`}>
            Choose Order Type:
          </Text>

          <View style={tw`flex flex-row gap-2 pb-4`}>
            {OrderType.map(item => {
              const checked = selectedId === item.id;
              return (
                <Pressable
                  key={item.id}
                  style={tw`flex-row items-center ml-6`}
                  accessibilityRole="radio"
                  onPress={() => setSelectedId(item.id)}
                  hitSlop={8}
                >
                  {/* Radio circle */}
                  <View
                    style={[
                      tw`border border-gray-400 rounded-full`,
                      {
                        width: 16,
                        height: 16,
                        alignItems: 'center',
                        justifyContent: 'center',
                      },
                    ]}
                  >
                    {checked && (
                      <View
                        style={[
                          tw`bg-blue-600 rounded-full`,
                          { width: 10, height: 10 },
                        ]}
                      />
                    )}
                  </View>

                  {/* Label */}

                  <Text style={tw`ml-2 text-xs text-gray-800`}>
                    {item.label}
                  </Text>
                </Pressable>
              );
            })}
          </View>

          {/* ---------------------------------------------------------date selector------------------------------------------- */}

          <View>
            <View style={tw`flex flex-row items-center justify-between px-6`}>
              <Text style={tw`text-xs font-semibold`}>Requested Date : </Text>
              <Pressable
                onPress={() => setOpen(true)}
                style={tw`h-10 border border-gray-200 rounded-sm w-50 `}
              ></Pressable>
            </View>
            <DatePicker
              modal
              open={open}
              date={date}
              onConfirm={date => {
                setOpen(false);
                setDate(date);
              }}
              onCancel={() => {
                setOpen(false);
              }}
            />
          </View>

          {/* ---------------------------------------------------project type--------------------------- */}

          <View
            style={tw`flex flex-row items-center justify-between px-6 pt-5`}
          >
            <Text style={tw`text-xs font-semibold `}>Project Type:</Text>

            <Dropdown
              style={tw`h-10 pl-2 border border-gray-200 w-50`}
              data={Data}
              labelField="label"
              valueField="value"
              search
            />
          </View>
          <View
            style={tw`flex flex-row items-center justify-between px-6 pt-5`}
          >
            <Text style={tw`text-xs font-semibold `}>Project Segment:</Text>

            <Dropdown
              style={tw`h-10 pl-2 border border-gray-200 w-50`}
              data={Data2}
              labelField="label"
              valueField="value"
            />
          </View>
          <View
            style={tw`flex flex-row items-center justify-between px-6 pt-5`}
          >
            <Text style={tw`text-xs font-semibold `}>Document No.:</Text>

            <View
              style={tw`flex-row items-center h-10 pl-2 border border-gray-200 w-50 `}
            >
              <TextInput
                style={tw`text-gray-800 `}
                placeholder="Document No."
                placeholderTextColor="#455560"
                autoCapitalize="none"
              />
            </View>
          </View>

          <View
            style={tw`flex flex-col items-start gap-2 px-6 pt-5 just2fy-between`}
          >
            <Text style={tw`text-xs font-semibold `}>
              Note for relationship manager:
            </Text>

            <View
              style={tw`flex-row items-center pl-2 border border-gray-200 w-84`}
            >
              <TextInput
                editable
                multiline
                numberOfLines={4}
                // maxLength={40}
                style={[
                  tw`h-20 p-2 text-gray-800`,
                  { textAlignVertical: 'top' },
                ]}
                placeholder="Note"
                placeholderTextColor="#455560"
                autoCapitalize="none"
              />
            </View>
          </View>
          <View
            style={tw`flex flex-row items-center justify-between px-5 pt-6`}
          >
            <Pressable
              onPress={handlePlaceOrder}
              style={tw` py-2 w-[48%] bg-[#B91C1C]  mt-3 rounded`}
            >
              <Text style={tw`text-xs text-center text-white`}>
                Place order request
              </Text>
            </Pressable>
            <Pressable
              onPress={onClose}
              style={tw` py-2 w-[48%] bg-[#fff]  mt-3 border border-gray-200 rounded`}
            >
              <Text style={tw`text-xs text-center text-black`}>Cancel</Text>
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default PlaceOrderModal;
