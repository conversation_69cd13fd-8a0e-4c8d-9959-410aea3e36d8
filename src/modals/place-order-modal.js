// MyModal.js
import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  Image,
  TouchableOpacity,
  Pressable,
} from 'react-native';
import tw from 'twrnc';

const OrderType = [
  { label: 'Blanket Order', id: 1 },
  { label: 'Normal Order', id: 2 },
];

const PlaceOrderModal = ({ visible, onClose }) => {
  const [selectedId, setSelectedId] = useState(null);

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View
        style={tw`items-center justify-center flex-1 bg-black bg-opacity-50`}
      >
        <View style={tw`w-[92%]   pt-2 bg-white rounded-lg`}>
          <View style={tw`flex flex-row items-center justify-end px-5 pt-1`}>
            <TouchableOpacity onPress={onClose}>
              <Image
                source={require('../assets/images/cross.png')}
                style={tw`w-3 h-3`}
              />
            </TouchableOpacity>
          </View>
          {/* --------------------------content------------------------- */}
          <Text style={tw`text-lg font-semibold text-center`}>
            {' '}
            You are placing an order request
          </Text>
          <Text style={tw`px-5 pt-1 pb-3 text-xs text-center`}>
            Soon after placing this request, you will receive an order
            confirmation link on
            <Text style={tw`font-semibold`}> <EMAIL> </Text>
            with the shipping cost from ALP.
          </Text>

          {/* --------------------------radio button------------------------- */}

          <Text style={tw`px-5 pb-2 text-base font-semibold`}>
            Choose Order Type:
          </Text>

          <View style={tw`flex flex-row gap-2 pb-4`}>
            {OrderType.map(item => {
              const checked = selectedId === item.id;
              return (
                <Pressable
                  key={item.id}
                  style={tw`flex-row items-center ml-6`}
                  accessibilityRole="radio"
                  onPress={() => setSelectedId(item.id)}
                  hitSlop={8}
                >
                  {/* Radio circle */}
                  <View
                    style={[
                      tw`border border-gray-400 rounded-full`,
                      {
                        width: 16,
                        height: 16,
                        alignItems: 'center',
                        justifyContent: 'center',
                      },
                    ]}
                  >
                    {checked && (
                      <View
                        style={[
                          tw`bg-blue-600 rounded-full`,
                          { width: 10, height: 10 },
                        ]}
                      />
                    )}
                  </View>

                  {/* Label */}

                  <Text style={tw`ml-2 text-xs text-gray-800`}>
                    {item.label}
                  </Text>
                </Pressable>
              );
            })}
          </View>

          {/* ---------------------------------------------------------date selector------------------------------------------- */}
        </View>
      </View>
    </Modal>
  );
};

export default PlaceOrderModal;
