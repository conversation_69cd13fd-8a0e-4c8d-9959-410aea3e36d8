/**
 * API Configuration for the DMS Application
 * 
 * This file contains all the configuration settings for API calls
 * including base URLs, timeouts, and other API-related settings.
 */

// Environment-based configuration
const ENV = __DEV__ ? 'development' : 'production';

const API_ENDPOINTS = {
  development: {
    BASE_URL: 'https://dev-api.your-domain.com', // Replace with your development API URL
    TIMEOUT: 15000, // 15 seconds for development
  },
  staging: {
    BASE_URL: 'https://staging-api.your-domain.com', // Replace with your staging API URL
    TIMEOUT: 12000, // 12 seconds for staging
  },
  production: {
    BASE_URL: 'https://api.your-domain.com', // Replace with your production API URL
    TIMEOUT: 10000, // 10 seconds for production
  },
};

// Get current environment configuration
const getCurrentConfig = () => {
  return API_ENDPOINTS[ENV] || API_ENDPOINTS.development;
};

// Export configuration
export const API_CONFIG = {
  ...getCurrentConfig(),
  
  // Common headers for all requests
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-App-Version': '1.0.0', // You can get this from package.json
  },
  
  // Request timeout settings
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
  
  // Authentication settings
  TOKEN_STORAGE_KEY: 'dealer_auth_token',
  REFRESH_TOKEN_STORAGE_KEY: 'dealer_refresh_token',
  
  // API response status codes
  STATUS_CODES: {
    SUCCESS: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_SERVER_ERROR: 500,
    SERVICE_UNAVAILABLE: 503,
  },
  
  // Error messages
  ERROR_MESSAGES: {
    NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
    TIMEOUT_ERROR: 'Request timed out. Please try again.',
    UNAUTHORIZED: 'Your session has expired. Please login again.',
    FORBIDDEN: 'You do not have permission to perform this action.',
    NOT_FOUND: 'The requested resource was not found.',
    SERVER_ERROR: 'Server error occurred. Please try again later.',
    UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
  },
};

// Export environment for debugging
export const CURRENT_ENV = ENV;

// Export helper functions
export const isProduction = () => ENV === 'production';
export const isDevelopment = () => ENV === 'development';

// API endpoint builder helper
export const buildApiUrl = (endpoint) => {
  const config = getCurrentConfig();
  return `${config.BASE_URL}${endpoint}`;
};

export default API_CONFIG;
