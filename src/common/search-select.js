import React, { useMemo, useRef, useState } from 'react';
import {
  View,
  TextInput,
  FlatList,
  TouchableOpacity,
  Text,
  Dimensions,
  Modal,
  TouchableWithoutFeedback,
  Pressable,
  findNodeHandle,
  UIManager,
} from 'react-native';
import tw from 'twrnc';

const { height: SCREEN_HEIGHT, width: SCREEN_WIDTH } = Dimensions.get('window');
const PANEL_MAX_HEIGHT = Math.min(Math.round(SCREEN_HEIGHT * 0.5), 320);

export default function SearchSelect({
  data = [],
  // single-select
  value,
  // multi-select
  multi = false,
  selectedValues = [],
  // common
  onChange,
  placeholder = 'Select item…',
  labelKey = 'name',
  valueKey = 'erp_item_code', // default to ERP code for your data
  disabled = false,
  closeOnSelect = !multi, // in multi, keep open by default
}) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [panelPos, setPanelPos] = useState({
    x: 16,
    y: 100,
    w: SCREEN_WIDTH - 32,
  });
  const triggerRef = useRef(null);

  const labelOf = item =>
    item?.[labelKey] ??
    item?.name ??
    item?.title ??
    item?.label ??
    item?.[valueKey] ??
    '';

  const skuOf = item => item?.erp_item_code ?? item?.SKU ?? '';

  // Stable, index-free key for compare & FlatList
  const keyOf = item =>
    String(
      item?.[valueKey] ??
        item?.erp_item_code ??
        item?.id ??
        item?._id ??
        item?.SKU ??
        item?.name ??
        '',
    );

  // Trigger text
  let triggerText = placeholder;
  if (multi) {
    const count = Array.isArray(selectedValues) ? selectedValues.length : 0;
    if (count > 0) triggerText = `${count} selected`;
  } else if (value) {
    triggerText = `${labelOf(value)}${
      skuOf(value) ? ` (${skuOf(value)})` : ''
    }`;
  }

  // Filter by name/title OR ERP code
  const filtered = useMemo(() => {
    const q = search.trim().toLowerCase();
    if (!q) return data;
    return data.filter(it => {
      const name = String(labelOf(it)).toLowerCase();
      const sku = String(skuOf(it)).toLowerCase();
      return name.includes(q) || sku.includes(q);
    });
  }, [data, search]);

  // Multi-select: check already picked (index-free)
  const isSelected = item => {
    if (!multi || !Array.isArray(selectedValues)) return false;
    const k = keyOf(item);
    return selectedValues.some(s => keyOf(s) === k);
  };

  // Position panel under trigger
  const measureTrigger = () => {
    const node = findNodeHandle(triggerRef.current);
    if (!node) return;
    UIManager.measureInWindow(node, (x, y, w, h) => {
      setPanelPos({
        x: Math.max(8, x),
        y: y + h + 6,
        w: Math.min(SCREEN_WIDTH - 16, w),
      });
    });
  };

  const openDropdown = () => {
    if (disabled) return;
    measureTrigger();
    setOpen(true);
  };

  const closeDropdown = () => {
    setSearch('');
    setOpen(false);
  };

  const handlePick = item => {
    onChange?.(item);
    if (closeOnSelect) closeDropdown();
    else setSearch('');
  };

  return (
    <View style={tw`w-full`}>
      <TouchableOpacity
        ref={triggerRef}
        disabled={disabled}
        onPress={openDropdown}
        activeOpacity={0.7}
        style={tw.style(
          'p-3 rounded border',
          disabled ? 'bg-gray-100 border-gray-200' : 'bg-white border-gray-300',
        )}
      >
        <Text style={tw`text-xs text-gray-700`} numberOfLines={1}>
          {triggerText}
        </Text>
      </TouchableOpacity>

      <Modal
        visible={open}
        transparent
        animationType="fade"
        onShow={measureTrigger}
        onRequestClose={closeDropdown}
        statusBarTranslucent
      >
        {/* Backdrop */}
        <TouchableWithoutFeedback onPress={closeDropdown}>
          <View style={tw`flex-1 bg-black/20`} />
        </TouchableWithoutFeedback>

        {/* Panel */}
        <View
          pointerEvents="box-none"
          style={tw.style('absolute', {
            left: panelPos.x,
            top: panelPos.y,
            width: panelPos.w,
          })}
        >
          <Pressable
            style={tw.style('bg-white border border-gray-300 rounded', {
              maxHeight: PANEL_MAX_HEIGHT,
              elevation: 6,
            })}
          >
            <TextInput
              placeholder="Search by name or SKU…"
              value={search}
              onChangeText={setSearch}
              style={tw`p-2 border-b border-gray-100 text-sm`}
              autoFocus
            />

            <FlatList
              data={filtered}
              keyExtractor={keyOf}
              keyboardShouldPersistTaps="handled"
              renderItem={({ item }) => {
                const picked = isSelected(item);
                return (
                  <TouchableOpacity
                    onPress={() => handlePick(item)}
                    activeOpacity={0.6}
                    style={tw.style(
                      'px-3 py-2 flex-row items-center justify-between',
                      picked && 'bg-blue-50 border-l-2 border-blue-500',
                    )}
                  >
                    <Text
                      style={tw.style(
                        'text-sm',
                        picked ? 'text-blue-700 font-medium' : 'text-black',
                      )}
                    >
                      {labelOf(item)}
                      {skuOf(item) ? ` (${skuOf(item)})` : ''}
                    </Text>
                    {picked ? (
                      <Text
                        style={tw`text-[10px] px-2 py-0.5 bg-blue-100 text-blue-700 rounded`}
                      >
                        Selected
                      </Text>
                    ) : (
                      <Text style={tw`text-xs text-gray-500`}>Tap to add</Text>
                    )}
                  </TouchableOpacity>
                );
              }}
              ListEmptyComponent={
                <Text style={tw`p-3 text-xs text-gray-500`}>No results</Text>
              }
            />
          </Pressable>
        </View>

        {/* click-through filler */}
        <TouchableWithoutFeedback onPress={closeDropdown}>
          <View style={tw`flex-1`} />
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
}
