// SearchableSelect.js
import React, { useState } from 'react';
import {
  View,
  TextInput,
  FlatList,
  TouchableOpacity,
  Text,
} from 'react-native';
import tw from 'twrnc';

const SearchSelect = ({
  data,
  onSelect,
  placeholder = 'SKU Search Product',
}) => {
  const [search, setSearch] = useState('');
  const [filteredData, setFilteredData] = useState(data);
  const [open, setOpen] = useState(false);

  const handleSearch = text => {
    setSearch(text);
    setFilteredData(
      data.filter(item => item.name.toLowerCase().includes(text.toLowerCase())),
    );
  };

  return (
    <View style={tw`w-full`}>
      <TouchableOpacity
        style={tw`p-2 bg-white border border-gray-300 rounded`}
        onPress={() => setOpen(!open)}
      >
        <Text style={tw`text-xs text-gray-400`}>{search || placeholder}</Text>
      </TouchableOpacity>

      {open && (
        <View style={tw`mt-1 bg-white border border-gray-300 rounded max-h-40`}>
          <TextInput
            placeholder="SKU Search Product"
            value={search}
            onChangeText={handleSearch}
            style={tw`p-2 border-b border-gray-100`}
          />
          <FlatList
            data={filteredData}
            keyExtractor={item => item.id}
            style={{ maxHeight: 160 }}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={tw`p-2`}
                onPress={() => {
                  onSelect(item);
                  setSearch(item.name);
                  setOpen(false);
                }}
              >
                <View style={tw`flex-row justify-between`}>
                  <Text>{item.name}</Text>
                  <Text style={tw`px-2 text-xs bg-gray-200 rounded `}>
                    {item.SKU}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
          />
        </View>
      )}
    </View>
  );
};

export default SearchSelect;
