import React, { useMemo, useRef, useState, useCallback } from 'react';
import {
  View,
  TextInput,
  FlatList,
  TouchableOpacity,
  Text,
  Dimensions,
  Modal,
  TouchableWithoutFeedback,
  Pressable,
  findNodeHandle,
  UIManager,
} from 'react-native';
import tw from 'twrnc';

const { height: SCREEN_HEIGHT, width: SCREEN_WIDTH } = Dimensions.get('window');
const PANEL_MAX_HEIGHT = Math.min(Math.round(SCREEN_HEIGHT * 0.5), 320);

// Memoized item component for better performance
const SearchSelectItem = React.memo(
  ({ item, picked, isExpired, labelOf, skuOf, onPress }) => {
    return (
      <TouchableOpacity
        onPress={() => !isExpired && onPress(item)}
        activeOpacity={isExpired ? 1 : 0.6}
        disabled={isExpired}
        style={tw.style(
          'px-3 py-2 flex-row items-center justify-between',
          isExpired && 'bg-red-50 opacity-60',
          !isExpired && picked && 'bg-blue-50 border-l-2 border-blue-500',
        )}
      >
        <Text
          style={tw.style(
            'text-md',
            isExpired
              ? 'text-red-400 line-through'
              : picked
              ? 'text-blue-700 font-medium'
              : 'text-black',
          )}
        >
          {labelOf(item)}
          <Text style={tw`uppercase text-sm text-gray-500`}>
            {' '}
            {skuOf(item) ? ` (${skuOf(item)})` : ''}
          </Text>
        </Text>
        {isExpired ? (
          <Text
            style={tw`text-[10px] px-2 py-0.5 bg-red-100 text-red-600 rounded`}
          >
            Expired
          </Text>
        ) : picked ? (
          ''
        ) : (
          ''
        )}
      </TouchableOpacity>
    );
  },
);

export default function SearchSelect({
  data = [],
  // single-select
  value,
  // multi-select
  multi = false,
  selectedValues = [],
  // common
  onChange,
  placeholder = 'Select item…',
  labelKey = 'name',
  valueKey = 'erp_item_code', // default to ERP code for your data
  disabled = false,
  closeOnSelect = !multi, // in multi, keep open by default
}) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [panelPos, setPanelPos] = useState({
    x: 16,
    y: 100,
    w: SCREEN_WIDTH - 32,
  });
  const triggerRef = useRef(null);

  const labelOf = useCallback(
    item =>
      item?.[labelKey] ??
      item?.name ??
      item?.title ??
      item?.label ??
      item?.[valueKey] ??
      '',
    [labelKey, valueKey],
  );

  const skuOf = useCallback(item => item?.erp_item_code ?? '', []);

  // Stable, index-free key for compare & FlatList
  const keyOf = useCallback(
    item => String(item?.[valueKey] ?? item?.erp_item_code ?? ''),
    [valueKey],
  );

  // Trigger text
  let triggerText = placeholder;
  if (multi) {
    const count = Array.isArray(selectedValues) ? selectedValues.length : 0;
    if (count > 0) triggerText = `${count} item(s) selected`;
  } else if (value) {
    triggerText = `${labelOf(value)}${
      skuOf(value) ? ` (${skuOf(value)})` : ''
    }`;
  }

  // Filter by name/title OR ERP code with result limit for performance
  const filtered = useMemo(() => {
    const q = search.trim().toLowerCase();
    if (!q) return data.slice(0, 100); // Limit initial results to 100 items

    const results = data.filter(it => {
      const name = String(labelOf(it)).toLowerCase();
      const sku = String(skuOf(it)).toLowerCase();
      return name.includes(q) || sku.includes(q);
    });

    // Limit search results to 50 items for better performance
    return results.slice(0, 50);
  }, [data, search, labelOf, skuOf]);

  // Multi-select: check already picked (index-free)
  const isSelected = useCallback(
    item => {
      if (!multi || !Array.isArray(selectedValues)) return false;
      const k = keyOf(item);
      return selectedValues.some(s => keyOf(s) === k);
    },
    [multi, selectedValues, keyOf],
  );

  // Position panel under trigger
  const measureTrigger = () => {
    const node = findNodeHandle(triggerRef.current);
    if (!node) return;
    UIManager.measureInWindow(node, (x, y, w, h) => {
      setPanelPos({
        x: Math.max(6, x),
        y: y + h + 6,
        w: Math.min(SCREEN_WIDTH - 16, w),
      });
    });
  };

  const openDropdown = () => {
    if (disabled) return;
    measureTrigger();
    setOpen(true);
  };

  const closeDropdown = () => {
    setSearch('');
    setOpen(false);
  };

  const handlePick = useCallback(
    item => {
      onChange?.(item);
      if (closeOnSelect) closeDropdown();
      else setSearch('');
    },
    [onChange, closeOnSelect],
  );

  // Memoized renderItem function for better performance
  const renderItem = useCallback(
    ({ item }) => {
      const picked = isSelected(item);
      const isExpired = item?.is_expired === 1;

      return (
        <SearchSelectItem
          item={item}
          picked={picked}
          isExpired={isExpired}
          labelOf={labelOf}
          skuOf={skuOf}
          onPress={handlePick}
        />
      );
    },
    [isSelected, labelOf, skuOf, handlePick],
  );

  // Memoized keyExtractor for better performance
  const keyExtractor = useCallback(
    (item, index) => keyOf(item, index),
    [keyOf],
  );

  return (
    <View style={tw`w-full`}>
      <TouchableOpacity
        ref={triggerRef}
        disabled={disabled}
        onPress={openDropdown}
        activeOpacity={0.7}
        style={tw.style(
          'p-3 rounded border',
          disabled ? 'bg-gray-100 border-gray-200' : 'bg-white border-gray-300',
        )}
      >
        <Text style={tw`text-xs text-gray-700`} numberOfLines={1}>
          {triggerText}
        </Text>
      </TouchableOpacity>

      <Modal
        visible={open}
        transparent
        animationType="fade"
        onShow={measureTrigger}
        onRequestClose={closeDropdown}
        statusBarTranslucent
      >
        {/* Backdrop */}
        <TouchableWithoutFeedback onPress={closeDropdown}>
          <View style={tw`flex-1 bg-black/20`} />
        </TouchableWithoutFeedback>

        {/* Panel */}
        <View
          pointerEvents="box-none"
          style={tw.style('absolute', {
            left: panelPos.x,
            top: panelPos.y,
            width: panelPos.w,
            zIndex: 1000,
          })}
        >
          <Pressable
            style={tw.style('bg-white border border-gray-300 rounded', {
              maxHeight: PANEL_MAX_HEIGHT,
              elevation: 6,
            })}
          >
            <TextInput
              placeholder="Search by name…"
              value={search}
              onChangeText={setSearch}
              style={tw`p-2 border-b border-gray-100 text-sm`}
              autoFocus
            />

            <FlatList
              data={filtered}
              keyExtractor={keyExtractor}
              keyboardShouldPersistTaps="handled"
              renderItem={renderItem}
              // Performance optimizations for large lists
              removeClippedSubviews={true}
              maxToRenderPerBatch={10}
              updateCellsBatchingPeriod={50}
              initialNumToRender={10}
              windowSize={10}
              getItemLayout={null} // Let FlatList calculate automatically
              ListEmptyComponent={
                <Text style={tw`p-3 text-xs text-gray-500`}>No results</Text>
              }
            />
          </Pressable>
        </View>

        {/* click-through filler */}
        <TouchableWithoutFeedback onPress={closeDropdown}>
          <View style={tw`flex-1`} />
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
}
