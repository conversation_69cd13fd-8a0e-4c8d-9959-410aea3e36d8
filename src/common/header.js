import React from 'react';
import { Image, Text, View } from 'react-native';
import tw from 'twrnc';
const Header = ({ Heading }) => {
  return (
    <View style={tw`flex flex-col justify-between`}>
      {/* <Icon name="menu" /> */}
      <Image
        source={require('../assets/images/logo.png')}
        style={tw`w-16 h-16 mx-auto my-.5`}
      />
      <View style={tw`bg-[#C4161C]  flex flex-row gap-2 py-2 items-center`}>
        {/* <Image
          source={require('../assets/images/back.png')}
          style={tw``}
        /> */}
        <Text style={tw`pl-2 text-base text-white text-start`}> {Heading}</Text>
      </View>
    </View>
  );
};

export default Header;
