import React, { useState } from 'react';
import {
  Image,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import tw from 'twrnc';
import Modal from 'react-native-modal';
import { useNavigation } from '@react-navigation/native';
const Header = ({ Heading }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const openModal = () => {
    setIsModalVisible(true);
  };
  const closeModal = () => {
    setIsModalVisible(false);
  };
  const navigation = useNavigation();

  // // Debug navigation object
  // console.log('Navigation object:', navigation);
  // console.log(
  //   'Navigation methods:',
  //   navigation ? Object.keys(navigation) : 'undefined',
  // );

  // Add error boundary for navigation
  const handleGoBack = () => {
    try {
      if (navigation && navigation.goBack) {
        navigation.goBack();
      } else {
        console.warn('Navigation.goBack not available');
      }
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  const handleNavigate = screenName => {
    try {
      if (navigation && navigation.navigate) {
        navigation.navigate(screenName);
      }
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  return (
    <>
      <View style={tw`flex flex-col justify-between mt-4`}>
        <View style={tw`flex flex-row items-center justify-between px-2`}>
          <Pressable onPress={openModal}>
            <Image
              source={require('../assets/images/menu-img.png')}
              style={tw`w-8 h-8`}
            />
          </Pressable>
          <Image
            source={require('../assets/images/logo.png')}
            style={tw`w-16 h-16 -translate-x-4`}
          />
          <View />
        </View>
        <View
          style={tw`bg-[#C4161C] flex flex-row gap-2 px-2 py-3 items-center`}
        >
          <Pressable onPress={handleGoBack}>
            <Image
              source={require('../assets/images/back-icon.png')}
              style={tw`w-7 h-7`}
            />
          </Pressable>

          <Text style={tw`pl-2 text-base text-white text-left`}>{Heading}</Text>
        </View>
      </View>

      {/* ----------------------------------------------modal------------------------------------------- */}

      <Modal
        isVisible={isModalVisible}
        onBackdropPress={closeModal}
        animationIn="slideInLeft"
        animationOut="slideOutLeft"
        style={tw`flex items-start flex-1 w-full h-full m-0`}
      >
        <View style={tw`flex-1 bg-[#C4161C] w-[80%]`}>
          {/* Header row */}
          <View
            style={tw`flex flex-row items-center justify-between pt-3 pl-8 pr-5 mb-4`}
          >
            <Image
              source={require('../assets/images/logo.png')}
              style={tw`w-16 h-16 -translate-x-4`}
              resizeMode="contain"
            />
            <TouchableOpacity onPress={closeModal}>
              <Image
                source={require('../assets/images/white-cross.png')}
                style={tw`w-10 h-10`}
              />
            </TouchableOpacity>
          </View>
          <View style={tw`flex flex-col gap-5 pt-8`}>
            <Pressable
              onPress={() => handleNavigate('Cart')}
              style={tw`pb-2 mx-5 border-b border-white border-opacity-40`}
            >
              <Text style={tw`text-sm text-white`}>Order Request</Text>
            </Pressable>
            <Pressable
              onPress={() => handleNavigate('Order_listing')}
              style={tw`pb-2 mx-5 border-b border-white border-opacity-40`}
            >
              <Text style={tw`text-sm text-white`}>My Orders</Text>
            </Pressable>
            <Pressable
              onPress={() => handleNavigate('Cart')}
              style={tw`pb-2 mx-5 border-b border-white border-opacity-40`}
            >
              <Text style={tw`text-sm text-white`}>Cart</Text>
            </Pressable>
            <Pressable
              onPress={() => handleNavigate('Profile_screen')}
              style={tw`pb-2 mx-5 border-b border-white border-opacity-40`}
            >
              <Text style={tw`text-sm text-white`}>Profile</Text>
            </Pressable>
            <Pressable
              style={tw`pb-2 mx-5 border-b border-white border-opacity-40`}
            >
              <Text style={tw`text-sm text-white`}>Logout</Text>
            </Pressable>
          </View>

          {/* Other drawer content goes here */}
        </View>
      </Modal>
    </>
  );
};

export default Header;
