// DealerTable.js
import React, { useState } from 'react';
import { View, Text, FlatList, Pressable } from 'react-native';
import tw from 'twrnc';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import {
  completeLogin,
  setDefaultDealerCode,
} from '../redux/reducers/authSlice';

// Header row component moved outside to prevent recreation on every render
const TableHeader = () => (
  <View style={tw`flex-row w-[100%] py-3 pl-10 bg-gray-100`}>
    <Text style={tw`flex-1 font-bold text-black`}>Dealer</Text>
    <Text style={tw`flex-1 font-bold text-black`}>Brand</Text>
    <Text style={tw`flex-1 font-bold text-black`}>Address</Text>
  </View>
);

// Row component moved outside to prevent recreation on every render
const TableRow = ({ item, index, selectedDealer, onPress }) => {
  const isSelected = selectedDealer === index;

  return (
    <Pressable
      onPress={() => onPress(index, item)}
      style={tw`flex-row items-center p-3 border-b border-gray-300`}
    >
      {/* Radio Button */}
      <View
        style={tw`items-center justify-center w-5 h-5 mr-3 border border-gray-500 rounded-full`}
      >
        {isSelected && <View style={tw`w-3 h-3 bg-blue-500 rounded-full`} />}
      </View>

      <Text style={tw`flex-1 text-black`}>{item.dealer_id}</Text>
      {/* <Text style={tw`flex-1 text-black`}>{item.brand}</Text> */}
      <Text style={tw`flex-1 text-black`}>{item.address}</Text>
    </Pressable>
  );
};

const DealerCodeTable = ({ data }) => {
  const [selectedDealer, setSelectedDealer] = useState(null);
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const authData = useSelector(state => state.auth_store);

  const handlePress = (index, item) => {
    setSelectedDealer(index);
    dispatch(setDefaultDealerCode(item?.dealer_id));
    // navigation.navigate('Cart');
    dispatch(completeLogin());
  };
  console.log('Data is:', authData);
  return (
    <View
      style={tw`mb-5 overflow-scroll  border border-gray-100 rounded-lg w-[100%]`}
    >
      <TableHeader />
      <FlatList
        data={data}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({ item, index }) => (
          <TableRow
            item={item}
            index={index}
            selectedDealer={selectedDealer}
            onPress={handlePress}
          />
        )}
        style={tw`max-h-80 w-[100%]`}
      />
    </View>
  );
};

export default DealerCodeTable;
