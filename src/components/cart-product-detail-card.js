import React, { useState } from 'react';
import { Image, Text, TextInput, TouchableOpacity, View } from 'react-native';
import tw from 'twrnc';

const CartProductDetailCard = ({ items, RemoveProduct }) => {
  const [quantity, setQuantity] = useState('1');

  // Handle case where items might be an object or undefined
  const item = items || {};
  const isExpired = item?.is_expired === 1;
  const unitPrice = parseFloat(item?.unit_price || 0);
  const qty = parseInt(quantity || 1, 10);
  const totalPrice = unitPrice * qty;

  return (
    <View
      style={tw.style(
        'flex flex-col px-2 pt-2 pb-3 rounded-lg mx-4 mb-8',
        isExpired ? 'bg-red-50 border border-red-200' : 'bg-[#F3F8FC]',
      )}
    >
      <TouchableOpacity
        onPress={RemoveProduct}
        style={tw`flex flex-row justify-end pb-2`}
      >
        <Image
          source={require('../assets/images/cross.png')}
          style={tw`w-4 h-4`}
        />
      </TouchableOpacity>

      {/* Product Info */}
      <View
        style={tw`flex flex-row justify-between pb-3 border-b border-gray-200`}
      >
        <View style={tw`flex flex-col`}>
          <Text
            style={tw.style(
              'text-xs font-medium',
              isExpired ? 'text-red-600' : 'text-gray-800',
            )}
          >
            {item?.erp_item_code || 'N/A'}
          </Text>
          {isExpired && (
            <Text style={tw`text-[10px] text-red-500 mt-1`}>EXPIRED</Text>
          )}
        </View>
        <View style={tw`flex flex-col w-[60%]`}>
          <Text
            style={tw.style(
              'text-xs',
              isExpired ? 'text-red-600 line-through' : 'text-gray-800',
            )}
          >
            {item?.name || 'Product Name'}
          </Text>
          {item?.ending_date && !isExpired && (
            <Text style={tw`text-[10px] text-gray-500 mt-1`}>
              Valid until: {item.ending_date}
            </Text>
          )}
        </View>
      </View>

      {/* Quantity Section */}
      <View
        style={tw`flex flex-row items-center justify-between py-2 border-b border-gray-200`}
      >
        <Text style={tw`text-xs`}>Qty</Text>

        <View
          style={tw`flex-row items-center bg-white border border-gray-200 w-[30%] rounded-md px-3`}
        >
          <TextInput
            style={tw`flex-1 h-8 py-0 text-sm text-gray-800`}
            placeholder="Qty"
            placeholderTextColor="#9BA4AF"
            value={quantity}
            onChangeText={setQuantity}
            keyboardType="numeric"
            editable={!isExpired}
          />
        </View>
      </View>

      {/* Price Section */}
      <View
        style={tw`flex flex-row items-center justify-between py-4 border-b border-gray-200`}
      >
        <View style={tw`flex flex-col`}>
          <Text style={tw`text-xs`}>Total Price</Text>
          {unitPrice > 0 && (
            <Text style={tw`text-[10px] text-gray-500`}>
              ₹{unitPrice.toFixed(2)} per unit
            </Text>
          )}
        </View>

        <View style={tw`flex-row items-center w-[30%] rounded-md px-3`}>
          {unitPrice > 0 ? (
            <Text
              style={tw.style(
                'text-sm font-bold',
                isExpired ? 'text-red-600' : 'text-gray-800',
              )}
            >
              ₹{totalPrice.toFixed(2)}
            </Text>
          ) : (
            <Text style={tw`text-sm text-gray-500`}>Price not available</Text>
          )}
        </View>
      </View>
    </View>
  );
};

export default CartProductDetailCard;
