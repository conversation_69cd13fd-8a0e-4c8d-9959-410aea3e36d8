import React, { useState, useEffect, useMemo } from 'react';
import {
  Image,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Platform,
} from 'react-native';
import tw from 'twrnc';

// parse a decimal safely; allow "1.", ".5", "1,234.56"
const parseDecimal = v => {
  if (v === null || v === undefined) return 0;
  const s = String(v).trim().replace(/,/g, '');
  if (s === '' || s === '.') return 0;
  const n = Number(s);
  return Number.isFinite(n) ? n : 0;
};

const clampMin = (n, min = 0) => (n < min ? min : n);

const CartProductDetailCard = ({
  items,
  RemoveProduct,
  onTotalChange,
  onQuantityChange,
}) => {
  const item = items || {};
  const itemId = item?.id ?? item?.erp_item_code ?? item?.sku ?? '';

  // initial qty text (keep as string to preserve user typing like "1.")
  const initialQtyText = useMemo(() => {
    const q = item?.qty ?? 1;
    return String(q);
  }, [itemId, item?.qty]);

  const [quantityText, setQuantityText] = useState(initialQtyText);

  // keep local state in sync if incoming qty changes
  useEffect(() => {
    setQuantityText(initialQtyText);
  }, [initialQtyText]);

  // derived numeric qty (allow decimals)
  const qty = clampMin(parseDecimal(quantityText), 0); // set min to 0 (or 0.1) if you want to forbid 0 set min=0.1

  const isExpired = item?.is_expired === 1;
  const unitPrice = parseDecimal(
    item?.unitPrice ?? item?.pricing?.unit_price ?? 0,
  );
  const packOf = parseDecimal(item?.packQty ?? 1);
  const gst = parseDecimal(item?.gst ?? item?.product?.gst ?? 0);

  const baseTotal = unitPrice * qty * packOf;
  const gstAmount = baseTotal * (gst / 100);
  const totalPrice = baseTotal + gstAmount;

  // notify parent when totals/qty change
  useEffect(() => {
    onTotalChange?.({
      itemId,
      baseTotal,
      gstAmount,
      totalPrice,
      quantity: qty,
    });
  }, [itemId, baseTotal, gstAmount, totalPrice, qty, onTotalChange]);

  const handleQuantityChange = text => {
    // allow only digits and one dot
    // but don't block user mid-typing like "1."
    const cleaned = text.replace(/[^\d.]/g, '');
    const parts = cleaned.split('.');
    const normalized =
      parts.length <= 2 ? cleaned : `${parts[0]}.${parts.slice(1).join('')}`;

    setQuantityText(normalized);

    const nextQty = clampMin(parseDecimal(normalized), 0);
    onQuantityChange?.({
      itemId,
      quantity: nextQty,
      item,
    });
  };

  return (
    <View
      style={tw.style(
        'flex flex-col px-2 pt-2 pb-3 rounded-lg mx-4 mb-0 mt-4',
        isExpired ? 'bg-red-50 border border-red-200' : 'bg-[#F3F8FC]',
      )}
    >
      <TouchableOpacity
        onPress={RemoveProduct}
        style={tw`flex flex-row justify-end pb-2`}
      >
        <Image
          source={require('../assets/images/cross.png')}
          style={tw`w-4 h-4`}
        />
      </TouchableOpacity>

      {/* Product Info */}
      <View
        style={tw`flex flex-row justify-between pb-3 border-b border-gray-200`}
      >
        <View style={tw`flex flex-col`}>
          <Text
            style={tw.style(
              'text-xs font-medium uppercase',
              isExpired ? 'text-red-600' : 'text-gray-800',
            )}
          >
            {item?.erp_item_code || item?.sku || 'N/A'}
          </Text>
          {isExpired && (
            <Text style={tw`text-[10px] text-red-500 mt-1`}>EXPIRED</Text>
          )}
        </View>
        <View style={tw`flex flex-col w-[60%]`}>
          <Text
            style={tw.style(
              'text-xs',
              isExpired ? 'text-red-600 line-through' : 'text-gray-800',
            )}
          >
            {item?.name || 'Product Name'} - Pack Of: {packOf}
          </Text>
        </View>
      </View>

      {/* Quantity */}
      <View
        style={tw`flex flex-row items-center justify-between py-2 border-b border-gray-200`}
      >
        <Text style={tw`text-xs`}>Qty</Text>
        <View
          style={tw`flex-row items-center bg-white border border-gray-200 w-[40%] rounded-md px-3`}
        >
          <TextInput
            style={tw`flex-1 h-8 py-0 text-sm text-gray-800`}
            placeholder="Qty"
            placeholderTextColor="#9BA4AF"
            value={quantityText}
            onChangeText={handleQuantityChange}
            keyboardType={Platform.OS === 'ios' ? 'decimal-pad' : 'numeric'}
            inputMode="decimal"
            editable={!isExpired}
          />
        </View>
      </View>

      {/* Price */}
      <View
        style={tw`flex flex-row items-center justify-between py-4 border-b border-gray-200`}
      >
        <View style={tw`flex flex-col`}>
          <Text style={tw`text-xs`}>Total Price (incl. GST)</Text>
        </View>
        <View style={tw`flex-row items-center w-[40%] rounded-md px-3`}>
          {unitPrice > 0 ? (
            <Text
              style={tw.style(
                'text-sm font-bold',
                isExpired ? 'text-red-600' : 'text-gray-800',
              )}
            >
              ₹{Number.isFinite(totalPrice) ? totalPrice.toFixed(2) : '0.00'}
            </Text>
          ) : (
            <Text style={tw`text-sm text-gray-500`}>Price not available</Text>
          )}
        </View>
      </View>
    </View>
  );
};

export default CartProductDetailCard;
