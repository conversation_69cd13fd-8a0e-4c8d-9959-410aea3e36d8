import React, { useState } from 'react';
import { Image, Text, TextInput, TouchableOpacity, View } from 'react-native';
import tw from 'twrnc';

const CartProductDetailCard = ({ items=[],RemoveProduct }) => {
  return (
    <View
      style={tw`flex flex-col px-2 pt-2 pb-3 bg-[#F3F8FC]  rounded-lg  mx-4  mb-8`}
    >
      <TouchableOpacity
        onPress={RemoveProduct}
        style={tw`flex flex-row justify-end pb-2`}
      >
        <Image
          source={require('../assets/images/cross.png')}
          style={tw`w-4 h-4`}
        />
      </TouchableOpacity>
      <View
        style={tw`flex flex-row justify-between pb-3 border-b border-gray-200 `}
      >
        <Text style={tw`text-xs`}>AC01061010</Text>
        <View style={tw`flex flex-col w-[50%] `}>
          <Text style={tw`text-xs`}>
            OP<PERSON> CELL ACCOSOUND SHEET 6MM : Pack of 15
          </Text>
        </View>
      </View>

      <View
        style={tw`flex flex-row items-center justify-between py-2 border-b border-gray-200 `}
      >
        <Text style={tw`text-xs`}>Qty</Text>

        <View
          style={tw`flex-row items-center  bg-white border border-gray-200  w-[30%] rounded-md px-3`}
        >
          <TextInput
            style={tw`flex-1 h-8 py-0 text-sm text-gray-800`}
            placeholder="Qty"
            placeholderTextColor="#9BA4AF"
            autoCapitalize="none"
            type="number"
          />
        </View>
      </View>

      <View
        style={tw`flex flex-row items-center justify-between py-4 border-b border-gray-200 `}
      >
        <Text style={tw`text-xs`}>Total Price</Text>

        <View style={tw`flex-row items-center   w-[30%] rounded-md px-3`}>
          <Text style={tw`text-sm font-bold text-gray-800`}>32,650.0</Text>
        </View>
      </View>
    </View>
  );
};

export default CartProductDetailCard;
