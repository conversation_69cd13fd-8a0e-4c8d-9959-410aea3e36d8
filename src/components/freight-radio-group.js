import React, { useState } from 'react';
import { View, Text, Pressable } from 'react-native';
import tw from 'twrnc';

export default function FreightRadioGroup({ onChange }) {
  const [selected, setSelected] = useState(null);

  const options = [
    { label: 'Freight Billed in Invoice', value: 'billed' },
    { label: 'Freight Paid', value: 'paid' },
    { label: 'To Pay', value: 'toPay' },
  ];

  const handleSelect = val => {
    setSelected(val);
    if (onChange) onChange(val);
  };

  return (
    <View style={tw`flex-col gap-3`}>
      {options.map(opt => {
        const checked = selected === opt.value;
        return (
          <Pressable
            key={opt.value}
            onPress={() => handleSelect(opt.value)}
            style={tw`flex-row items-center ml-6`}
            accessibilityRole="radio"
            accessibilityState={{ checked }}
            hitSlop={8}
          >
            {/* Radio circle */}
            <View
              style={[
                tw`border border-gray-400 rounded-full`,
                {
                  width: 20,
                  height: 20,
                  alignItems: 'center',
                  justifyContent: 'center',
                },
              ]}
            >
              {checked && (
                <View
                  style={[
                    tw`bg-blue-600 rounded-full`,
                    { width: 12, height: 12 },
                  ]}
                />
              )}
            </View>

            {/* Label */}
            <Text style={tw`ml-2 text-gray-800`}>{opt.label}</Text>
          </Pressable>
        );
      })}
    </View>
  );
}
