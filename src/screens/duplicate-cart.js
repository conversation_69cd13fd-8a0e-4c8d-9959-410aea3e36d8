import React, { useEffect, useState, useCallback } from 'react';
import { FlatList, Pressable, Text, View } from 'react-native';
import tw from 'twrnc';
import Header from '../common/header';
import SearchSelect from '../common/search-select';
import CartProductDetailCard from '../components/cart-product-detail-card';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { AllProductsAPI } from '../redux/actions/allProductsAction';

const productKey = item =>
  String(
    item?.erp_item_code ?? item?.id ?? item?._id ?? item?.SKU ?? item?.name,
  );

const DuplicateCartScreen = () => {
  const [selectedItems, setSelectedItems] = useState([]);

  const dealer_code = useSelector(s => s.auth_store?.defaultDealerCode);
  const allProducts = useSelector(s => s.all_product?.all_products_slice) || [];

  const navigation = useNavigation();
  const dispatch = useDispatch();

  useEffect(() => {
    if (!dealer_code) return;
    const p = dispatch(AllProductsAPI({ dealer_code }));
    if (typeof p?.unwrap === 'function') {
      p.unwrap().catch(err =>
        console.error('Error fetching the products:', err),
      );
    } else {
      p?.catch?.(err => console.error('Error fetching the products:', err));
    }
  }, [dispatch, dealer_code]);

  const handleSelect = useCallback(item => {
    setSelectedItems(prev => {
      const k = productKey(item);
      if (prev.some(p => productKey(p) === k)) return prev; // dedupe
      return [...prev, item];
    });
  }, []);

  const handleRemove = useCallback(itemToRemove => {
    setSelectedItems(prev => {
      const k = productKey(itemToRemove);
      return prev.filter(p => productKey(p) !== k);
    });
  }, []);

  const handleShipment = () => navigation.navigate('Shipment_Detail');

  return (
    <FlatList
      data={[{ key: 'content' }]}
      keyExtractor={() => 'static'}
      renderItem={null}
      keyboardShouldPersistTaps="handled"
      nestedScrollEnabled
      contentContainerStyle={tw`bg-white min-h-full pb-6`}
      ListHeaderComponent={
        <View>
          <Header Heading="New Order Request" />

          <View style={tw`flex-row items-center justify-between px-4 my-5`}>
            <Text style={tw`text-sm font-semibold text-black`}>
              SHOP-ORDER/001
            </Text>
            <Text style={tw`text-[#C4161C] text-sm font-semibold`}>
              Product
            </Text>
          </View>

          <View style={tw`px-4 mt-4`}>
            <Text style={tw`mb-2 text-xs font-semibold text-black`}>
              Add products to cart
            </Text>

            <SearchSelect
              data={allProducts}
              multi
              selectedValues={selectedItems} // inform picker what's chosen
              closeOnSelect={true} // close after each pick (set false to keep open)
              onChange={handleSelect}
              placeholder="Choose products…"
              labelKey="name" // adjust if your field differs (e.g., product_name)
              valueKey="erp_item_code" // unified unique key
            />
          </View>

          {selectedItems.length > 0 && (
            <View style={tw`mt-2`}>
              {selectedItems.map(item => (
                <View key={productKey(item)} style={tw`mb-3`}>
                  <CartProductDetailCard
                    dealer={item}
                    RemoveProduct={() => handleRemove(item)}
                  />
                </View>
              ))}
            </View>
          )}
        </View>
      }
      ListFooterComponent={
        <View style={tw`px-4 mt-8 mb-6`}>
          <Pressable
            onPress={handleShipment}
            style={tw`px-4 py-3 bg-[#B91C1C] rounded-lg`}
          >
            <Text style={tw`text-center text-white`}>Save & Next</Text>
          </Pressable>
        </View>
      }
    />
  );
};

export default DuplicateCartScreen;
