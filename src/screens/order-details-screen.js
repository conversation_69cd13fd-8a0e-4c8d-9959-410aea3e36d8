import React from 'react';
import { ScrollView, Text, TextInput, View } from 'react-native';
import tw from 'twrnc';

const OrderDetailsScreen = () => {
  return (
    <ScrollView style={tw`flex-1 p-5 bg-white`}>
      <Text style={tw`text-xl font-semibold`}> View</Text>
      <View style={tw`flex flex-col gap-1 mt-3`}>
        <Text style={tw`text-sm`}> Order ID</Text>

        <View
          style={tw`flex-row items-center h-10 px-3 border border-gray-200 rounded-lg bg-gray-50`}
        >
          <TextInput
            style={tw`flex-1 text-sm text-gray-800`}
            editable={false}
          />
        </View>
      </View>
      <View style={tw`pt-3 pb-5 mt-5 border border-gray-200 rounded-lg`}>
        <View style={tw`pb-3 border-b border-gray-200`}>
          <Text style={tw`px-3 text-sm font-semibold`}>Personal Details</Text>
        </View>
        <View style={tw`flex flex-col gap-1 mt-3`}>
          <Text style={tw`px-3 text-sm`}>Name</Text>

          <View
            style={tw`flex-row items-center h-10 px-3 mx-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>

        <View style={tw`flex flex-col gap-1 mt-5`}>
          <Text style={tw`px-3 text-sm`}>GST Number</Text>

          <View
            style={tw`flex-row items-center h-10 px-3 mx-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>
      </View>

      <View style={tw`pt-3 pb-5 mt-5 mb-10 border border-gray-200 rounded-lg`}>
        <View style={tw`pb-3 border-b border-gray-200`}>
          <Text style={tw`px-3 text-sm font-semibold`}>Shipping Details</Text>
        </View>
        <View style={tw`flex flex-col gap-1 mt-3`}>
          <Text style={tw`px-3 text-sm`}>Shipping Address</Text>

          <View
            style={tw`flex-row items-center h-10 px-3 mx-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>

        <View style={tw`flex flex-col gap-1 mt-5`}>
          <Text style={tw`px-3 text-sm`}>City</Text>

          <View
            style={tw`flex-row items-center h-10 px-3 mx-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>
        <View style={tw`flex flex-col gap-1 mt-5`}>
          <Text style={tw`px-3 text-sm`}>State</Text>

          <View
            style={tw`flex-row items-center h-10 px-3 mx-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>
        <View style={tw`flex flex-col gap-1 mt-5`}>
          <Text style={tw`px-3 text-sm`}>Pincode</Text>

          <View
            style={tw`flex-row items-center h-10 px-3 mx-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>
        <View style={tw`flex flex-col gap-1 mt-5`}>
          <Text style={tw`px-3 text-sm`}>Shipment Price</Text>

          <View
            style={tw`flex-row items-center h-10 px-3 mx-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

export default OrderDetailsScreen;
