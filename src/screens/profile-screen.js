import Header from '../common/header';
import React from 'react';
import { Text, TextInput, View } from 'react-native';
import tw from 'twrnc';

const ProfileScreen = () => {
  return (
    <View style={tw`flex-1 bg-white`}>
      <Header Heading="My Profile" />

      <View style={tw`flex flex-col gap-3 px-4 mt-5`}>
        <View style={tw`flex flex-col gap-1 `}>
          <Text style={tw`text-sm`}> Company Name </Text>

          <View
            style={tw`flex-row items-center h-10 px-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>
        <View style={tw`flex flex-col gap-1 `}>
          <Text style={tw`text-sm`}>Address</Text>

          <View
            style={tw`flex-row items-center h-10 px-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>
        <View style={tw`flex flex-col gap-1 `}>
          <Text style={tw`text-sm`}>City</Text>

          <View
            style={tw`flex-row items-center h-10 px-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>
        <View style={tw`flex flex-col gap-1 `}>
          <Text style={tw`text-sm`}>State</Text>

          <View
            style={tw`flex-row items-center h-10 px-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>
        <View style={tw`flex flex-col gap-1 `}>
          <Text style={tw`text-sm`}>Pincode</Text>

          <View
            style={tw`flex-row items-center h-10 px-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>
        <View style={tw`flex flex-col gap-1 `}>
          <Text style={tw`text-sm`}>GST Number</Text>

          <View
            style={tw`flex-row items-center h-10 px-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>
        <View style={tw`flex flex-col gap-1 `}>
          <Text style={tw`text-sm`}>Credit Limits</Text>

          <View
            style={tw`flex-row items-center h-10 px-3 border border-gray-200 rounded-lg bg-gray-50`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              editable={false}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export default ProfileScreen;
