import React, { useState } from 'react';
import { Button, Pressable, ScrollView, Text, View } from 'react-native';
import tw from 'twrnc';
import Header from '../common/header';
import SearchSelect from '../common/search-select';
import CartProductDetailCard from '../components/cart-product-detail-card';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { logout } from '../redux/reducers/authSlice';

const dealers = [
  { id: '1', name: 'Dealer One', SKU: 'abc123' },
  { id: '2', name: 'Dealer Two', SKU: 'abc123' },
  { id: '3', name: 'Dealer Three', SKU: 'abc123' },
  { id: '4', name: 'Dealer Four', SKU: 'abc123' },
];
const CartScreen = () => {
  const [selectedDealer, setSelectedDealer] = useState(null);
  const navigation = useNavigation();
  const handleSelect = dealer => {
    setSelectedDealer(dealer); // triggers showing the card
  };
  const handleRemove = () => {
    setSelectedDealer(null);
  };

  const handleShipment = () => {
    navigation.navigate('Shipment_Detail');
  };
  const dispatch = useDispatch();
  // dispatch(logout());

  return (
    <ScrollView style={tw`relative h-full bg-white`}>
      <Header Heading="New Order Request" />

      <View style={tw`flex flex-row items-center justify-between px-4 my-5`}>
        <Text style={tw`text-sm font-semibold text-black`}>SHOP-ORDER/001</Text>
        <Text style={tw`text-[#C4161C] text-sm font-semibold`}>Product</Text>
      </View>

      {/* ---------------------------------product detail card--------------------------------- */}

      {selectedDealer && (
        <View style={tw`mt-2`}>
          <CartProductDetailCard
            dealer={selectedDealer}
            RemoveProduct={handleRemove}
          />
        </View>
      )}
      <View style={tw`px-4`}>
        <Text style={tw`mb-2 text-xs font-semibold text-black`}>
          Add more products to cart
        </Text>
        <SearchSelect data={dealers} onSelect={handleSelect} />
      </View>

      <View
        style={tw`absolute flex flex-row items-center justify-center w-full top-180`}
      >
        {/* Button */}
        <Pressable
          onPress={handleShipment}
          style={tw`px-4 py-2 bg-[#B91C1C] rounded-lg w-[40%]`}
        >
          <Text style={tw`text-center text-white `}>Save & Next</Text>
        </Pressable>
      </View>
    </ScrollView>
  );
};

export default CartScreen;
