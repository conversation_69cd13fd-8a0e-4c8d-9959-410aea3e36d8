// src/screens/cart-screen.js
import React, { useEffect, useState, useCallback } from 'react';
import { FlatList, Pressable, Text, View } from 'react-native';
import tw from 'twrnc';
import Header from '../common/header';
import SearchSelect from '../common/search-select';
import CartProductDetailCard from '../components/cart-product-detail-card';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import {
  AllProductsAPI,
  PDetailsPrice,
} from '../redux/actions/allProductsAction';
import { GetDealerCart } from '../redux/actions/ordersAction';

const norm = v =>
  String(v ?? '')
    .trim()
    .toLowerCase();

const productKey = item =>
  String(
    item?.erp_item_code ?? item?.id ?? item?._id ?? item?.sku ?? item?.name,
  );

const CartScreen = () => {
  const [selectedItems, setSelectedItems] = useState([]);

  const dealer_code = useSelector(s => s.auth_store?.defaultDealerCode);
  const allProducts = useSelector(s => s.all_product?.all_products_slice) || [];

  const navigation = useNavigation();
  const dispatch = useDispatch();
  const [subTotal, setSubTotal] = useState(0);
  const [totalGst, setTotalGst] = useState(0);
  const [cartItems, setCartItems] = useState([]);

  useEffect(() => {
    if (!dealer_code) return;
    const p = dispatch(AllProductsAPI({ dealer_code }));
    if (typeof p?.unwrap === 'function') {
      p.unwrap().catch(err =>
        console.error('Error fetching the products:', err),
      );
    } else {
      p?.catch?.(err => console.error('Error fetching the products:', err));
    }
  }, [dispatch, dealer_code]);

  const handleSelect = useCallback(
    item => {
      const code = productKey(item);
      const codeNorm = norm(code);

      // Determine if we're adding or removing (based on current state)
      const isAlreadySelected = selectedItems.some(
        p => norm(productKey(p)) === codeNorm,
      );

      if (isAlreadySelected) {
        // Toggle OFF: remove item; no need to fetch details
        setSelectedItems(prev =>
          prev.filter(p => norm(productKey(p)) !== codeNorm),
        );
        return;
      }

      // Toggle ON: first add the item optimistically
      setSelectedItems(prev => {
        if (prev.some(p => norm(productKey(p)) === codeNorm)) return prev;
        return [...prev, item];
      });

      // Then fetch details and merge into the matching selected item
      dispatch(
        PDetailsPrice({
          dealer_code,
          erp_item_code: code, // your thunk can handle the case
        }),
      ).then(res => {
        const payload = res?.payload?.data?.data;
        const pricing = payload?.pricing;
        const product = payload?.product;
        const respCodeNorm = norm(pricing?.erp_item_code);

        if (!respCodeNorm) return;

        setSelectedItems(prev =>
          prev.map(p =>
            norm(p?.erp_item_code) === respCodeNorm
              ? { ...p, pricing, product }
              : p,
          ),
        );
      });
    },
    [dispatch, dealer_code, selectedItems],
  );
  console.log('Selected Items:', selectedItems);
  const handleRemove = useCallback(itemToRemove => {
    const keyToRemove = productKey(itemToRemove);
    setSelectedItems(prev => {
      const filtered = prev.filter(p => productKey(p) !== keyToRemove);
      console.log(
        'Selected items before:',
        prev.length,
        'after:',
        filtered.length,
      );
      return filtered;
    });

    setCartItems(prev => {
      const filtered = prev.filter(p => productKey(p) !== keyToRemove);
      console.log('Cart items before:', prev.length, 'after:', filtered.length);
      return filtered;
    });
  }, []);

  const handleShipment = () => navigation.navigate('Shipment_Detail');
  // for cart
  useEffect(() => {
    dispatch(GetDealerCart({ dealerCode: dealer_code })).then(response => {
      console.log('Cart Response:', response);
      const products = response?.payload?.data?.data?.details?.products;
      if (products) {
        setSelectedItems(
          products.map(items => {
            return {
              pad: items?.pad,
              sku: items?.alp_code,
              name: items?.title,
              packQty: items?.packaging_quantity_wholesaler_box,
              unitPrice: items?.unit_price,
              boxPrice: items?.boxPrice,
              qty: items?.quantity,
              discount: items?.discount ?? 0,
              total_price: items?.total_price,
              gst: items?.gst,
              gstAmount: items?.gstNumber,
              finalPrice: items?.final_price,
              msp_price: items?.msp,
            };
          }),
        );
      } else {
        setCartItems([]);
        setSelectedItems([]);
      }
    });
  }, [dealer_code, dispatch]);
  useEffect(() => {
    setCartItems(
      selectedItems.map(items => {
        return {
          pad:
            parseFloat(items?.unit_price || 0) *
              (items?.qty || 1) *
              (items?.product?.packaging_quantity_wholesaler_box || 1) ||
            items?.pad,
          sku: items?.erp_item_code || items?.sku,
          name: items?.name,
          packQty:
            items?.product?.packaging_quantity_wholesaler_box || items?.packQty,
          unitPrice: items?.pricing?.unit_price || items?.unitPrice,
          boxPrice:
            items?.boxPrice ||
            parseFloat(items?.unit_price || 0) *
              (items?.product?.packaging_quantity_wholesaler_box || 1),
          qty: 1 || items?.qty,
          discount: 0,
          total_price:
            items?.total_price ||
            parseFloat(items?.unit_price || 0) *
              (items?.qty || 1) *
              (items?.product?.packaging_quantity_wholesaler_box || 1),
          gst: items?.product?.gst || items?.gst,
          gstAmount:
            items?.gstAmount ||
            parseFloat(items?.unit_price || 0) *
              (items?.qty || 1) *
              (items?.product?.packaging_quantity_wholesaler_box || 1) *
              (items?.product?.gst / 100),
          finalPrice:
            items?.finalPrice ||
            parseFloat(items?.unit_price || 0) *
              (items?.qty || 1) *
              (items?.product?.packaging_quantity_wholesaler_box || 1) +
              parseFloat(items?.unit_price || 0) *
                (items?.qty || 1) *
                (items?.product?.packaging_quantity_wholesaler_box || 1) *
                (items?.product?.gst / 100),
          msp_price: items?.pricing?.msp_price || items?.msp_price,
        };
      }),
    );
  }, [selectedItems]);
  console.log('Cart Items:', cartItems);
  useEffect(() => {
    setSubTotal(cartItems.reduce((acc, item) => acc + item?.total_price, 0));
    setTotalGst(cartItems.reduce((acc, item) => acc + item?.gstAmount, 0));
  }, [cartItems]);
  return (
    <FlatList
      data={[{ key: 'content' }]}
      keyExtractor={() => 'static'}
      renderItem={null}
      keyboardShouldPersistTaps="handled"
      nestedScrollEnabled
      contentContainerStyle={tw`bg-white min-h-full pb-6`}
      ListHeaderComponent={
        <View>
          <Header Heading="New Order Request" />

          <View style={tw`flex-row items-center justify-between px-4 my-5`}>
            <Text style={tw`text-sm font-semibold text-black`}>
              SHOP-ORDER/001
            </Text>
            <Text style={tw`text-[#C4161C] text-sm font-semibold`}>
              Product
            </Text>
          </View>

          <View style={tw`px-4 mt-4`}>
            <Text style={tw`mb-2 text-xs font-semibold text-black`}>
              Add products to cart
            </Text>

            <SearchSelect
              data={allProducts}
              multi
              selectedValues={selectedItems}
              closeOnSelect={true} // close after each pick; set to false to keep open
              onChange={handleSelect}
              placeholder="Choose products…"
              labelKey="name" // change if your list uses 'title'
              valueKey="erp_item_code"
            />
          </View>

          {cartItems.length > 0 && (
            <View style={tw`mt-2`}>
              {cartItems.map(item => (
                <View key={productKey(item)} style={tw`mb-3`}>
                  <CartProductDetailCard
                    items={item}
                    RemoveProduct={() => handleRemove(item)}
                    subTotal={subTotal}
                    totalGst={totalGst}
                    setSubTotal={setSubTotal}
                    setTotalGst={setTotalGst}
                  />
                </View>
              ))}
            </View>
          )}
        </View>
      }
      ListFooterComponent={
        <View style={tw`px-4 border-t pt-8 mb-6`}>
          <View
            style={tw`flex flex-col gap-2 bg-[#F3F8FC] p-3 rounded-lg mb-4`}
          >
            <View style={tw`flex flex-row justify-between`}>
              <Text>Subtotal :</Text>
              <Text>₹{subTotal}</Text>
            </View>
            <View style={tw`flex flex-row justify-between`}>
              <Text>Total GST :</Text>
              <Text>₹{totalGst}</Text>
            </View>
            <View
              style={tw`flex flex-row justify-between border-t border-gray-500 pt-2`}
            >
              <Text>Grand Total :</Text>
              <Text>₹{subTotal + totalGst}</Text>
            </View>
          </View>

          <Pressable
            onPress={handleShipment}
            style={tw`px-4 py-3 bg-[#B91C1C] rounded-lg`}
          >
            <Text style={tw`text-center text-white`}>Save & Next</Text>
          </Pressable>
        </View>
      }
    />
  );
};

export default CartScreen;
