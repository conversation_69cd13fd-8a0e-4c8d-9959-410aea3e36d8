// src/screens/cart-screen.js
import React, {
  useEffect,
  useState,
  useCallback,
  useMemo,
  useRef,
} from 'react';
import { FlatList, Pressable, Text, View } from 'react-native';
import tw from 'twrnc';
import Header from '../common/header';
import SearchSelect from '../common/search-select';
import CartProductDetailCard from '../components/cart-product-detail-card';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import {
  AllProductsAPI,
  PDetailsPrice,
} from '../redux/actions/allProductsAction';
import { DealerSaveToCart, GetDealerCart } from '../redux/actions/ordersAction';

const norm = v =>
  String(v ?? '')
    .trim()
    .toLowerCase();

// ONE identity for both arrays
const productKey = item =>
  norm(item?.erp_item_code ?? item?.sku ?? item?.id ?? item?._id ?? item?.name);

// safe number
const num = (v, d = 0) => {
  if (v === null || v === undefined) return d;
  const n = Number(String(v).replace(/,/g, ''));
  return Number.isFinite(n) ? n : d;
};

// single source of truth for a cart row (derived numbers)
const toCartRow = src => {
  const key = productKey(src);
  const erp = src?.erp_item_code ?? src?.sku ?? '';
  const name = src?.name ?? src?.title ?? src?.product?.title ?? '';
  const packQty = num(
    src?.packQty ?? src?.product?.packaging_quantity_wholesaler_box ?? 1,
    1,
  );
  const unit = num(
    src?.unitPrice ?? src?.pricing?.unit_price ?? src?.unit_price,
    0,
  );
  const qty = Math.max(0, num(src?.qty ?? 1, 1));
  const gstPct = num(src?.gst ?? src?.product?.gst, 0);

  const pricePerBox = unit * packQty;
  const base = unit * packQty * qty; // subtotal (excl. GST)
  const gstAmount = base * (gstPct / 100);
  const finalPrice = base + gstAmount;

  return {
    // identity + display
    __key: key,
    erp_item_code: erp,
    name,
    // inputs
    packQty,
    unitPrice: unit,
    qty,
    gst: gstPct,
    // derived
    pad: pricePerBox,
    boxPrice: pricePerBox,
    total_price: base,
    gstAmount,
    finalPrice,
    // carry-through if present
    discount: num(src?.discount, 0),
    msp_price: num(src?.pricing?.msp_price ?? src?.msp_price, 0),
  };
};

const CartScreen = () => {
  const [selectedItems, setSelectedItems] = useState([]); // keep raw+pricing+qty here
  const [cartItems, setCartItems] = useState([]); // derived rows for UI

  const dealer_code = useSelector(s => s.auth_store?.defaultDealerCode);
  const allProducts = useSelector(s => s.all_product?.all_products_slice) || [];
  const authData = useSelector(state => state.auth_store);
  const balanceCredit = authData?.dealer_codes?.filter(
    item => item.dealer_id === dealer_code,
  )[0]?.balance_credit;
  const navigation = useNavigation();
  const dispatch = useDispatch();
  // fetch product list
  useEffect(() => {
    if (!dealer_code) return;
    const p = dispatch(AllProductsAPI({ dealer_code }));
    if (typeof p?.unwrap === 'function') {
      p.unwrap().catch(err =>
        console.error('Error fetching the products:', err),
      );
    } else {
      p?.catch?.(err => console.error('Error fetching the products:', err));
    }
  }, [dispatch, dealer_code]);

  // select / deselect product
  const handleSelect = useCallback(
    item => {
      const k = productKey(item);
      setSelectedItems(prev => {
        const exists = prev.some(p => productKey(p) === k);
        if (exists) {
          // toggle OFF
          return prev.filter(p => productKey(p) !== k);
        }
        // toggle ON (default qty 1 if missing)
        const next = [...prev, { ...item, qty: num(item?.qty, 1) }];
        return next;
      });

      // fetch pricing, then merge
      dispatch(
        PDetailsPrice({
          dealer_code,
          erp_item_code: item?.erp_item_code ?? item?.sku ?? '',
        }),
      ).then(res => {
        const payload = res?.payload?.data?.data;
        const pricing = payload?.pricing;
        const product = payload?.product;
        const respCode = norm(pricing?.erp_item_code);
        if (!respCode) return;
        setSelectedItems(prev =>
          prev.map(p =>
            norm(p?.erp_item_code ?? p?.sku) === respCode
              ? { ...p, pricing, product }
              : p,
          ),
        );
      });
    },
    [dispatch, dealer_code],
  );

  // remove item from both arrays
  const handleRemove = useCallback(itemToRemove => {
    const k = productKey(itemToRemove);
    setSelectedItems(prev => prev.filter(p => productKey(p) !== k));
    setCartItems(prev => prev.filter(p => productKey(p) !== k));
  }, []);

  // quantity change from card (decimal OK)
  const handleQuantityChange = useCallback(({ itemId, quantity, item }) => {
    const k = itemId ? norm(itemId) : productKey(item);

    // 1) update selectedItems.qty
    setSelectedItems(prev =>
      prev.map(p => (productKey(p) === k ? { ...p, qty: quantity } : p)),
    );

    // 2) update cartItems row (recompute derived fields)
    setCartItems(prev =>
      prev.map(row =>
        productKey(row) === k
          ? toCartRow({ ...row, qty: quantity }) // merge qty, recompute
          : row,
      ),
    );
  }, []);

  // hydrate from server cart once (merge)
  useEffect(() => {
    if (!dealer_code) return;
    dispatch(GetDealerCart({ dealerCode: dealer_code })).then(response => {
      const products = response?.payload?.data?.data?.details?.products;
      if (!products) return;

      const server = products.map(items => ({
        erp_item_code: items?.alp_code ?? items?.sku ?? items?.erp_item_code,
        name: items?.title ?? items?.name,
        qty: num(items?.quantity, 1),
        packQty: num(items?.packaging_quantity_wholesaler_box, 1),
        unitPrice: num(items?.unit_price, 0),
        gst: num(items?.gst, 0),
        discount: num(items?.discount, 0),
        msp_price: num(items?.msp, 0),
      }));

      setSelectedItems(prev => {
        const seen = new Set(prev.map(productKey));
        const merged = [...prev];
        for (const si of server) {
          const k = productKey(si);
          if (!seen.has(k)) merged.push(si);
        }
        return merged;
      });
    });
  }, [dealer_code, dispatch]);

  // derive cart items from selectedItems whenever selectedItems changes
  useEffect(() => {
    setCartItems(selectedItems.map(toCartRow));
  }, [selectedItems]);

  // totals from cartItems
  const { subTotal, totalGst, grandTotal } = useMemo(() => {
    let sub = 0,
      gst = 0;
    for (const it of cartItems) {
      sub += num(it?.total_price, 0);
      gst += num(it?.gstAmount, 0);
    }
    return { subTotal: sub, totalGst: gst, grandTotal: sub + gst };
  }, [cartItems]);

  const handleShipment = () => {
    try {
      console.log('Navigating to Shipment_Detail');
      if (navigation && navigation.navigate) {
        navigation.navigate('Shipment_Detail');
      } else {
        console.error('Navigation object or navigate method not available');
      }
    } catch (error) {
      console.error('Navigation error in handleShipment:', error);
    }
  };
  const saveTimer = useRef();

  useEffect(() => {
    clearTimeout(saveTimer.current);
    // console.log('Save to cart started');
    if (cartItems.length === 0) {
      const payload = {
        dealerCode: dealer_code,
        details: {
          products: [],
          subTotal: 0,
          totalGST: 0,
          grandTotal: 0,
        },
      };
      dispatch(DealerSaveToCart(payload))
        .then(() => {
          // console.log('Save to cart ended with 0 cartItems');
        })
        .catch(console.error);
    } else {
      saveTimer.current = setTimeout(() => {
        const payload = {
          dealerCode: dealer_code,
          details: {
            products: cartItems.map(it => ({
              pad: it.pad,
              alp_code: it.sku,
              title: it.name,
              packaging_quantity_wholesaler_box: it.packQty,
              unit_price: it.unitPrice,
              boxPrice: it.boxPrice,
              quantity: it.qty,
              discount: it.discount ?? 0,
              total_price: it.total_price,
              gst: it.gst,
              gstNumber: it.gstAmount,
              final_price: it.finalPrice,
              msp: it.msp_price,
            })),
            subTotal,
            totalGst,
            grandTotal,
          },
        };
        dispatch(DealerSaveToCart(payload))
          .then(() => {
            // console.log('Save to cart ended');
          })
          .catch(console.error);
      }, 800);
    }

    return () => clearTimeout(saveTimer.current);
  }, [cartItems, dealer_code, subTotal, totalGst, grandTotal, dispatch]);
  return (
    <FlatList
      data={[{ key: 'content' }]}
      keyExtractor={() => 'static'}
      renderItem={null}
      keyboardShouldPersistTaps="handled"
      nestedScrollEnabled
      contentContainerStyle={tw`bg-white min-h-full pb-6`}
      ListHeaderComponent={
        <View>
          <Header Heading="New Order Request" />

          <View style={tw`flex-row items-center justify-between px-4 my-5`}>
            <Text style={tw`text-xl font-bold text-black`}>Cart</Text>
            <Text style={tw`text-[#C4161C] font-bold`}>Product</Text>
          </View>

          <View style={tw`px-4 mt-4`}>
            <Text style={tw`mb-2 text-xs font-semibold text-black`}>
              Add products to cart
            </Text>
            <SearchSelect
              data={allProducts}
              multi
              selectedValues={selectedItems}
              closeOnSelect={true}
              onChange={handleSelect}
              placeholder="Choose products…"
              labelKey="name"
              valueKey="erp_item_code"
            />
          </View>

          {cartItems.length > 0 && (
            <View style={tw`mt-2`}>
              {cartItems.map(item => (
                <View key={productKey(item)} style={tw`mb-3`}>
                  <CartProductDetailCard
                    items={item}
                    RemoveProduct={() => handleRemove(item)}
                    onQuantityChange={handleQuantityChange}
                    onTotalChange={() => {}}
                  />
                </View>
              ))}
            </View>
          )}
        </View>
      }
      ListFooterComponent={
        <View style={tw`px-4 border-t pt-8 mt-6 mb-6`}>
          <View style={tw`flex flex-col gap-2 bg-gray-200 p-3 rounded-lg mb-4`}>
            <View style={tw`flex flex-row justify-between`}>
              <Text>Subtotal :</Text>
              <Text>₹{subTotal.toFixed(2)}</Text>
            </View>
            <View style={tw`flex flex-row justify-between`}>
              <Text>Total GST :</Text>
              <Text>₹{totalGst.toFixed(2)}</Text>
            </View>
            <View
              style={tw`flex flex-row justify-between border-t border-gray-500 pt-2`}
            >
              <Text style={tw`font-bold`}>Grand Total :</Text>
              <Text style={tw`font-bold`}>₹{grandTotal.toFixed(2)}</Text>
            </View>
            <View
              style={tw`flex flex-row justify-between border-t border-gray-500 pt-2`}
            >
              <Text style={tw`font-bold`}>Balance Credit Limit :</Text>
              <Text style={tw`font-bold`}>₹{balanceCredit}</Text>
            </View>
          </View>

          <Pressable
            onPress={handleShipment}
            disabled={cartItems.length < 1}
            style={tw.style(
              'px-4 py-3 rounded-lg',
              cartItems.length < 1 ? 'bg-gray-300' : 'bg-[#B91C1C]',
            )}
          >
            <Text style={tw`text-center text-white`}>Save & Next</Text>
          </Pressable>
        </View>
      }
    />
  );
};

export default CartScreen;
