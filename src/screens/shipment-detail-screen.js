import React, { useState } from 'react';
import { Pressable, ScrollView, Text, View } from 'react-native';
import tw from 'twrnc';
import Header from '../common/header';
import FreightRadioGroup from '../components/freight-radio-group';
import PlaceOrderModal from '../modals/place-order-modal';

const ShipmentDetailScreen = () => {
  const handleFreightChange = val => {
    console.log('Selected:', val);
  };
  const [isModalVisible, setModalVisible] = useState(false);
  const handleCloseModal = () => {
    setModalVisible(false);
  };
  const handlePlaceOrder = () => {
    setModalVisible(true);
  };
  return (
    <ScrollView style={tw`bg-white `}>
      <Header Heading="New Order Request" />

      {/* ----------------------------------address card------------------------------ */}
      <View
        style={tw`px-3 pb-4 mx-4 mt-10 mb-3 border border-gray-300 rounded-lg`}
      >
        <Text style={tw`py-3 text-sm font-semibold`}>Shipment Details</Text>
        <View
          style={tw`flex flex-row items-center justify-between pb-3 border-b border-gray-100`}
        >
          <Text style={tw`text-xs`}>City:</Text>
          <Text style={tw`text-xs`}>DELHI-DELHI-110092</Text>
        </View>
        <View
          style={tw`flex flex-row items-center justify-between py-3 border-b border-gray-100`}
        >
          <Text style={tw`text-xs`}>Address:</Text>
          <Text style={tw`text-xs`}>C 109, GALI NO. 1, GANESH NAGAR</Text>
        </View>
      </View>

      {/* ----------------------------------payment details card------------------------------ */}

      <View
        style={tw`px-3 pb-5 mx-4 mt-2 mb-3 border border-gray-300 rounded-lg`}
      >
        <View style={tw`flex flex-col`}>
          <View style={tw`flex flex-row items-center justify-between py-3 `}>
            <Text style={tw`text-xs`}>Subtotal:</Text>
            <Text style={tw`text-xs`}>₹ 27675</Text>
          </View>
          <View
            style={tw`flex flex-row items-center justify-between pb-3 border-b border-gray-100`}
          >
            <Text style={tw`text-xs`}>GST:</Text>
            <Text style={tw`text-xs`}>+ ₹4981.5</Text>
          </View>
        </View>
        <View
          style={tw`flex flex-row items-center justify-between py-3 border-b border-gray-100`}
        >
          <Text style={tw`text-xs font-semibold`}>Grand Total:</Text>
          <Text style={tw`text-xs`}>₹32656.5</Text>
        </View>
        <View style={tw`flex flex-row items-center justify-between py-3 `}>
          <Text style={tw`text-xs`}>Balanced Credit Limit:</Text>
          <Text style={tw`text-xs`}> ₹6941152.13</Text>
        </View>

        <View style={tw`flex flex-row justify-between pt-5 pb-9`}>
          <View>
            <Text style={tw`text-xs font-semibold`}>Shipment:</Text>
          </View>

          <FreightRadioGroup onChange={handleFreightChange} />
        </View>

        <Text style={tw`text-xs `}>
          ALP Shop will manage your shipment, and you'll handle payment directly
          to the trucking company.
        </Text>
        <Pressable
          onPress={handlePlaceOrder}
          style={tw`px-4 py-2 bg-[#B91C1C] rounded-lg w-[60%] mx-auto mt-10`}
        >
          <Text style={tw`text-center text-white `}>
            Place an order request
          </Text>
        </Pressable>
      </View>

      <PlaceOrderModal visible={isModalVisible} onClose={handleCloseModal} />
    </ScrollView>
  );
};

export default ShipmentDetailScreen;
