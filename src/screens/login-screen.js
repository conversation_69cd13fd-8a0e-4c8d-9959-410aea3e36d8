import React, { useEffect, useState } from 'react';
import {
  Image,
  Text,
  View,
  TextInput,
  Pressable,
  ActivityIndicator,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import tw from 'twrnc';
import SelectDealerCodeModal from '../modals/select-dealer-code-modal';
import { LoginDealerData } from '../redux/actions/authActions';
import { logout, completeLogin } from '../redux/reducers/authSlice';
import Alertify from '../redux/services/alertify';

const LoginScreen = () => {
  const dispatch = useDispatch();
  const authLoading = useSelector(state => state.auth?.loading ?? false);
  const authData = useSelector(state => state.auth_store);

  const [email, setEmail] = useState('');
  const [pwd, setPwd] = useState('');
  const [showPwd, setShowPwd] = useState(false);
  const [errors, setErrors] = useState({});
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [dealerCodes, setDealerCodes] = useState([]);

  const handleDealerSelect = (selectedDealer, index) => {
    console.log('Selected dealer:', selectedDealer);
    // Set the selected dealer as default
    // You can dispatch an action to save the selected dealer if needed
    // For now, just complete the login
    dispatch(completeLogin());
  };

  const validateForm = () => {
    const newErrors = {};
    if (!email.trim()) newErrors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(email))
      newErrors.email = 'Please enter a valid email address';
    if (!pwd.trim()) newErrors.pwd = 'Password is required';
    else if (pwd.length < 6)
      newErrors.pwd = 'Password must be at least 6 characters';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;
    dispatch(LoginDealerData({ email_or_mobile: email.trim(), password: pwd }))
      .then(res => {
        const response = res?.payload?.data;
        console.log('Login Response:', response);
        console.log('Dealer Codes:', response?.user?.dealer_details);

        const dealerCodesFromAPI = response?.user?.dealer_details || [];
        setDealerCodes(dealerCodesFromAPI);

        if (dealerCodesFromAPI.length > 1) {
          // Multiple dealers - show modal for selection
          Alertify.success('Login successful! Please select a dealer.');
          setDealerCodes(dealerCodesFromAPI);
          setIsModalVisible(true);
        } else if (dealerCodesFromAPI.length === 1) {
          // Single dealer - auto-complete login
          Alertify.success('Login successful!');
          dispatch(completeLogin());
        } else {
          // No dealers - still complete login (edge case)
          Alertify.success('Login successful!');
          dispatch(completeLogin());
        }

        return response;
      })
      .catch(err => {
        console.log('Login Error:', err);
        Alertify.error('Error:', err);
      });
  };
  useEffect(() => {
    console.log('Auth Loading:', authLoading);
    console.log('Auth Data:', authData);
    console.log('isModalVisible', isModalVisible);
  }, [authLoading, authData, isModalVisible]);
  return (
    <View
      style={tw`flex flex-col justify-start pt-10 gap-16 items-center bg-[#0474b8] h-full px-4`}
    >
      <Image
        source={require('../assets/images/logo.png')}
        style={tw`w-32 h-32`}
      />

      <View
        style={tw`flex flex-col w-[100%] items-center bg-white px-5 py-12 rounded-2xl`}
      >
        <Text style={tw`text-lg font-semibold text-gray-800 mb-7`}>
          Sign in to your account
        </Text>

        {/* Email */}
        <View style={tw`w-full max-w-sm mb-4`}>
          <Text style={tw`mb-2 text-sm font-medium text-gray-800`}>
            <Text style={tw`font-bold text-red-600`}>* </Text>Your Email
          </Text>
          <View
            style={tw`flex-row items-center px-3 border border-gray-200 rounded-lg bg-gray-50 h-11`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              placeholder="<EMAIL>"
              placeholderTextColor="#9BA4AF"
              autoCapitalize="none"
              keyboardType="email-address"
              value={email}
              onChangeText={setEmail}
            />
          </View>
          {errors.email && (
            <Text style={tw`mt-1 text-xs text-red-600`}>{errors.email}</Text>
          )}
        </View>

        {/* Password */}
        <View style={tw`w-full max-w-sm mb-4`}>
          <Text style={tw`mb-2 text-sm font-medium text-gray-800`}>
            <Text style={tw`font-bold text-red-600`}>* </Text>Password
          </Text>
          <View
            style={tw`flex-row items-center px-3 border border-gray-200 rounded-lg bg-gray-50 h-11`}
          >
            <TextInput
              style={tw`flex-1 text-sm text-gray-800`}
              placeholder="••••••••"
              placeholderTextColor="#9BA4AF"
              secureTextEntry={!showPwd}
              value={pwd}
              onChangeText={setPwd}
            />
            <Pressable onPress={() => setShowPwd(!showPwd)}>
              <Text style={tw`text-xs font-semibold text-blue-600`}>
                {showPwd ? 'Hide' : 'Show'}
              </Text>
            </Pressable>
          </View>
          {errors.pwd && (
            <Text style={tw`mt-1 text-xs text-red-600`}>{errors.pwd}</Text>
          )}
        </View>

        {/* Button */}
        <Pressable
          onPress={handleLogin}
          disabled={authLoading}
          style={tw`px-4 py-2 bg-[#B91C1C] px-10 mt-7 rounded ${
            authLoading ? 'opacity-70' : ''
          }`}
        >
          {authLoading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={tw`text-white `}>Login</Text>
          )}
        </Pressable>
      </View>

      <SelectDealerCodeModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        dealerCodes={dealerCodes}
        onDealerSelect={handleDealerSelect}
      />
    </View>
  );
};

export default LoginScreen;
