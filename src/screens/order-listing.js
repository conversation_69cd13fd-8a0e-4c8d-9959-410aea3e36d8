import Header from '../common/header';
import React from 'react';
import { Pressable, ScrollView, StyleSheet, Text, View } from 'react-native';
import SearchSelect from '../common/search-select';
import tw from 'twrnc';

const OrderData = [
  { id: '1', name: 'Dealer One', SKU: 'abc123' },
  { id: '2', name: 'Dealer Two', SKU: 'abc123' },
  { id: '3', name: 'Dealer Three', SKU: 'abc123' },
  { id: '4', name: 'Dealer Four', SKU: 'abc123' },
];

const OrderCard = [
  {
    id: '1',
  },
  {
    id: '2',
  },
  {
    id: '3',
  },
  {
    id: '4',
  },
];

const OrderListing = () => {
  return (
    <>
      <ScrollView style={tw`relative h-full pb-10 bg-white`}>
        <Header Heading="Order Request" />

        <View style={tw`px-12 py-5`}>
          <SearchSelect data={OrderData} placeholder="Search Order" />
        </View>
        {/* ----------------------------order card------------------------- */}
        <View style={tw`pb-10`}>
          {OrderCard.map(item => {
            return (
              <View
                key={item.id}
                style={tw`px-5 py-5 mt-3 bg-[#F3F8FC] rounded-lg mx-3`}
              >
                <View style={tw`flex-row items-center justify-between`}>
                  <Text style={tw`text-sm font-semibold text-black`}>
                    SHOP-ORDER/001
                  </Text>
                  <Text style={tw`text-[#000] text-lg font-semibold`}>⁝</Text>
                </View>
                <View
                  style={tw`flex-row border-b border-[#E4E4E4] pb-1 pt-2 items-center justify-between`}
                >
                  <Text style={tw`text-xs text-black`}>Requested Date</Text>
                  <Text style={tw`text-[#000] text-xs`}>26/11/2024</Text>
                </View>
                <View
                  style={tw`flex-row border-b border-[#E4E4E4] pb-1 pt-5 items-center justify-between`}
                >
                  <Text style={tw`text-xs text-black`}>Order By</Text>
                  <Text style={tw`text-[#000] text-xs`}>ABC Pvt. Ltd.</Text>
                </View>
                <View
                  style={tw`flex-row border-b border-[#E4E4E4] pb-1 pt-5 items-center justify-between`}
                >
                  <Text style={tw`text-xs text-black`}>Placed at</Text>
                  <Text style={tw`text-[#000] text-xs`}>26-11-2024</Text>
                </View>
                <View
                  style={tw`flex-row border-b border-[#E4E4E4] pb-1 pt-5 items-center justify-between`}
                >
                  <Text style={tw`text-xs text-black`}>Order Total</Text>
                  <Text style={tw`text-[#000] text-xs`}>21000</Text>
                </View>
              </View>
            );
          })}
        </View>
      </ScrollView>
      {/* <Pressable
        style={tw`rounded-full h-16 w-16 absolute bottom-[10%]  right-10  bg-[#C21616] flex justify-center items-center `}
      >
        <View style={tw`flex flex-col items-center justify-start`}>
          <Text style={tw`-mt-2 text-lg text-black text-white`}>+</Text>
          <Text style={tw`-mt-2 text-xs font-semibold text-black text-white`}>
            Request
          </Text>
        </View>
      </Pressable> */}
    </>
  );
};

export default OrderListing;
