import Header from '../common/header';
import React from 'react';
import {
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
} from 'react-native';
import SearchSelect from '../common/search-select';
import tw from 'twrnc';
import { Dropdown } from 'react-native-element-dropdown';
import { useNavigation } from '@react-navigation/native';

const Data = [];

const OrderCard = [
  {
    id: '1',
  },
  {
    id: '2',
  },
  {
    id: '3',
  },
  {
    id: '4',
  },
];

const OrderListing = () => {
  const navigation = useNavigation();
  return (
    <>
      <ScrollView style={tw`relative h-full pb-10 bg-white`}>
        <Header Heading="Order Request" />

        <View style={tw`px-12 py-5`}>
          <Dropdown
            placeholder="Search Order"
            style={tw`w-full h-10 pl-2 border border-gray-200 rounded-lg p`}
            data={Data}
            labelField="label"
            valueField="value"
            search
            placeholderStyle={{ color: 'rgba(111,111,111,0.5)', fontSize: 13 }} // ← string
            selectedTextStyle={{ color: '#455560', fontSize: 13 }}
            itemTextStyle={{ color: '#455560', fontSize: 13 }}
            inputSearchStyle={{ color: '#455560', fontSize: 13 }}
          />
        </View>

        {/* ----------------------------order card------------------------- */}
        <View style={tw`pb-10`}>
          {OrderCard.map(item => {
            return (
              <Pressable
                onPress={() => navigation.navigate('Order_details')}
                key={item.id}
                style={tw`px-5 py-5 mt-3 bg-[#F3F8FC] rounded-lg mx-3`}
              >
                <View style={tw`flex-row items-center justify-between`}>
                  <Text style={tw`text-sm font-semibold text-black`}>
                    SHOP-ORDER/001
                  </Text>
                  <Text style={tw`text-[#000] text-lg font-semibold`}>⁝</Text>
                </View>
                <View
                  style={tw`flex-row border-b border-[#E4E4E4] pb-1 pt-2 items-center justify-between`}
                >
                  <Text style={tw`text-xs text-black`}>Requested Date</Text>
                  <Text style={tw`text-[#000] text-xs`}>26/11/2024</Text>
                </View>
                <View
                  style={tw`flex-row border-b border-[#E4E4E4] pb-1 pt-5 items-center justify-between`}
                >
                  <Text style={tw`text-xs text-black`}>Order By</Text>
                  <Text style={tw`text-[#000] text-xs`}>ABC Pvt. Ltd.</Text>
                </View>
                <View
                  style={tw`flex-row border-b border-[#E4E4E4] pb-1 pt-5 items-center justify-between`}
                >
                  <Text style={tw`text-xs text-black`}>Placed at</Text>
                  <Text style={tw`text-[#000] text-xs`}>26-11-2024</Text>
                </View>
                <View
                  style={tw`flex-row border-b border-[#E4E4E4] pb-1 pt-5 items-center justify-between`}
                >
                  <Text style={tw`text-xs text-black`}>Order Total</Text>
                  <Text style={tw`text-[#000] text-xs`}>21000</Text>
                </View>
              </Pressable>
            );
          })}
        </View>
      </ScrollView>
    </>
  );
};

export default OrderListing;
