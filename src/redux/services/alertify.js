// src/services/alertify.native.js
import { Platform, ToastAndroid, Alert } from 'react-native';

const ANDROID_DURATION = ToastAndroid.SHORT;

function toAndroidGravity(pos = 'bottom-right') {
  // Map your web positions to Android gravity
  // Note: Android toasts don't support "right/left" alignment, only gravity.
  const p = (pos || '').toLowerCase();
  if (p.includes('top')) return ToastAndroid.TOP;
  if (p.includes('center') || p.includes('middle')) return ToastAndroid.CENTER;
  return ToastAndroid.BOTTOM; // default
}

const showAndroidToast = (message, position) => {
  ToastAndroid.showWithGravity(
    String(message ?? ''),
    ANDROID_DURATION,
    toAndroidGravity(position),
  );
};

const showiOSAlert = (title, message = '') => {
  Alert.alert(title, String(message ?? ''), [{ text: 'OK' }], {
    cancelable: true,
  });
};

const Alertify = {
  /**
   * Show a success message.
   * @param {string} alert_text
   * @param {'top'|'bottom'|'center'|'top-right'|'bottom-right'|'top-left'|'bottom-left'} position
   */
  success: (alert_text, position = 'bottom-right') => {
    if (Platform.OS === 'android') {
      showAndroidToast(alert_text, position);
    } else {
      showiOSAlert('Success', alert_text);
    }
  },

  /**
   * Show an error message.
   * @param {string} alert_text
   * @param {'top'|'bottom'|'center'|'top-right'|'bottom-right'|'top-left'|'bottom-left'} position
   */
  error: (alert_text, position = 'bottom-right') => {
    if (Platform.OS === 'android') {
      showAndroidToast(alert_text, position);
    } else {
      showiOSAlert('Error', alert_text);
    }
  },

  /**
   * Show a default/confirm-style message with optional action.
   * On Android: toast (no action UI). On iOS: Alert with optional action button.
   * @param {string} alert_text
   * @param {string} [action_text]
   * @param {() => void} [action_callback]
   * @param {'top'|'bottom'|'center'|'top-right'|'bottom-right'|'top-left'|'bottom-left'} [position]
   */
  default: (alert_text, action_text, action_callback, position = 'bottom') => {
    if (Platform.OS === 'android') {
      // Android Toasts don't support buttons; show as a toast
      showAndroidToast(alert_text, position);
      if (typeof action_callback === 'function') {
        // If you really need an action on Android, switch to Alert:
        // Alert.alert('', alert_text, [{ text: action_text || 'OK', onPress: action_callback }]);
        // For now, we just call it immediately to keep API parity minimal:
        action_callback();
      }
    } else {
      const buttons =
        action_text && action_callback
          ? [
              { text: 'Cancel', style: 'cancel' },
              { text: action_text, onPress: action_callback },
            ]
          : [{ text: 'OK' }];
      Alert.alert('', String(alert_text ?? ''), buttons, { cancelable: true });
    }
  },
};

export default Alertify;
