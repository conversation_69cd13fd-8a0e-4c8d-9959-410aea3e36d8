import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import store from '../store';

const ProjectUrl = 'https://alp-be.hkstest.uk';

// Helper function to get auth token
const getAuthToken = async () => {
  try {
    // Try to get token from store first, then AsyncStorage
    const token =
      store?.getState()?.auth_store?.token ||
      (await AsyncStorage.getItem('token'));
    return token;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

// Helper function to handle 401 errors
const handle401Error = errorMessage => {
  const message = errorMessage || 'Your session is expired';
  Alert.alert('Session Expired', message, [
    {
      text: 'OK',
      onPress: () => {
        // Clear token from AsyncStorage on session expiry
        AsyncStorage.removeItem('token');
      },
    },
  ]);
};

// Centralized error handling function
export function handleApiError(error, rejectWithValue) {
  console.error(error);

  const message = error?.message || 'An error occurred';
  Alert.alert('Error', message, [{ text: 'OK' }]);

  return rejectWithValue(error);
}

// Helper function to handle API requests using fetch
const handleApiRequest = async (method, path, data = {}, headers = {}) => {
  try {
    const token = await getAuthToken();
    const url = `${ProjectUrl}${path}`;
    console.log('URL:', url);
    const config = {
      method: method.toUpperCase(),
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': '69420',
        ...headers,
      },
    };

    // Add authorization header if token exists
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add body for POST, PUT, PATCH requests
    if (['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) && data) {
      config.body = JSON.stringify(data);
    }

    const response = await fetch(url, config);

    // Handle 401 errors
    if (response.status === 401) {
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = errorData.error || 'Your session is expired';
      handle401Error(errorMessage);
      throw new Error(errorMessage);
    }

    // Handle other HTTP errors
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP error! status: ${response.status}`,
      );
    }

    // Return the response data
    const responseData = await response.json();
    return { data: responseData }; // Mimic axios response structure
  } catch (error) {
    console.error(`API ${method.toUpperCase()} error:`, error);
    throw error;
  }
};

// Exported API functions
export const postApi = (path, data, headers) =>
  handleApiRequest('post', path, data, headers);

export const getApi = (path, headers) =>
  handleApiRequest('get', path, {}, headers);

export const patchApi = (path, data, headers) =>
  handleApiRequest('patch', path, data, headers);

export const putApi = (path, data, headers) =>
  handleApiRequest('put', path, data, headers);

export const deleteApi = (path, data = {}, headers) =>
  handleApiRequest('delete', path, data, headers);
