import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { authListener } from './listeners/authListeners';
import authSlice from './reducers/authSlice';

const persistConfig = {
  key: 'root',
  storage: AsyncStorage, // ✅ RN storage
  whitelist: ['auth_store', 'common_store'], // keep as you had
};

const rootReducer = combineReducers({
  auth_store: authSlice,
  //   admin_dealers: adminDealersSlice,
  //   bd_person_store: NewOrderRequestSlice,
  //   all_product: AllProductsSlice,
  //   all_lists: AllLists,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefault =>
    getDefault({
      thunk: true,
      serializableCheck: false,
      immutableCheck: false,
    }).prepend(authListener.middleware), // ✅ side-effects here
});

export const persistor = persistStore(store);
export default store;
