import { createSlice } from '@reduxjs/toolkit';
import { LoginDealerData } from '../actions/authActions';

const initialState = {
  is_Login: false,
  email_or_mobile: null,
  token: '',
  role: '',
  seller_code: '',
  dealer_codes: [],
  name: null,
  session_start_time: null,
  defaultDealerCode: '',
};

const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24h

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // reducers must be pure (no AsyncStorage/alerts here)
    logout: () => {
      return { ...initialState };
    },
    ResetLocalState: () => {
      return { ...initialState };
    },
    checkSessionTimeout: state => {
      const start = state.session_start_time;
      if (start && Date.now() - start > SESSION_TIMEOUT) {
        // Just reset state; show alerts in UI/middleware if desired
        return { ...initialState };
      }
      return state;
    },
    setDefaultDealerCode: (state, action) => {
      state.defaultDealerCode = action.payload;
    },
    completeLogin: state => {
      // This action completes the login process after dealer selection
      state.is_Login = true;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(LoginDealerData.fulfilled, (state, { payload }) => {
        const dealerCodes = payload?.data?.user?.dealer_details ?? [];

        // Only set is_Login = true if there's 1 or 0 dealers (no selection needed)
        state.is_Login = dealerCodes.length <= 1;

        state.name = payload?.data?.user?.name ?? null;
        state.token = payload?.data?.token ?? '';
        state.email_or_mobile = payload?.data?.user?.email ?? null;
        state.seller_code = payload?.data?.user?.seller_code ?? '';
        state.role = payload?.data?.user?.role ?? '';
        state.dealer_codes = dealerCodes;
        state.session_start_time = Date.now();
      })
      .addCase(LoginDealerData.rejected, (state, action) => {
        // optionally store an error flag/message here
        console.error('Login failed:', action.error?.message);
      });
  },
});

export const {
  logout,
  ResetLocalState,
  checkSessionTimeout,
  setDefaultDealerCode,
  completeLogin,
} = authSlice.actions;

export default authSlice.reducer;
