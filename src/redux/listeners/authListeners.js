import { createListenerMiddleware, isAnyOf } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { logout } from '../reducers/authSlice';
import { LoginDealerData } from '../actions/authActions';
// import { Alert } from 'react-native'; // or your Alertify if you want to toast here

export const authListener = createListenerMiddleware();

// Save token + session start on successful login
authListener.startListening({
  matcher: isAnyOf(LoginDealerData.fulfilled),
  effect: async action => {
    try {
      const token = action?.payload?.data?.token;
      if (token) {
        await AsyncStorage.setItem('token', token);
        await AsyncStorage.setItem('session_start_time', String(Date.now()));
      }
    } catch {
      // swallow storage errors (optional: report)
    }
  },
});

// Remove token + session start on logout
authListener.startListening({
  actionCreator: logout,
  effect: async () => {
    try {
      await AsyncStorage.removeItem('token');
      await AsyncStorage.removeItem('session_start_time');
      // If you ever want to fully clear persisted Redux:
      // await AsyncStorage.removeItem('persist:root');
    } catch {
      // swallow storage errors
    }
  },
});
