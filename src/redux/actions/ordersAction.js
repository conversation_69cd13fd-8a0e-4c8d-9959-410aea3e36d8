import { createAsyncThunk } from '@reduxjs/toolkit';
import { deleteApi, postApi } from '../services/apiServices';
import {
  ALL_PRODUCTS,
  DEALER_PUNCH_ORDER,
  DEALER_SAVE_TO_CART,
  DELETE_CART,
  GET_DEALER_CART,
  GET_DEALER_ORDER_REQUESTS_LIST,
  GET_DEALER_ORDERS_LIST,
  PRODUCT_WITH_PRICE,
} from '../constants/apiConstants';
import Alertify from '../services/alertify';

/*---------------- Get Dealer Cart ---------------- */
export const GetDealerCart = createAsyncThunk(
  'get-dealer-cart/dealer',
  async reqData => {
    try {
      //   const { dealer_code } = reqData;
      const response = await postApi(GET_DEALER_CART, reqData);
      return response;
    } catch (error) {
      console.log('Error Get Dealer Cart API:"', error);
      Alertify.error(error.message);
      return Promise.reject(error);
    }
  },
);
/*---------------- Save to Cart ---------------- */
export const DealerSaveToCart = createAsyncThunk(
  'save-to-cart/dealer',
  async reqData => {
    try {
      const response = await postApi(DEALER_SAVE_TO_CART, reqData);
      return response;
    } catch (error) {
      console.log('Error Save to Cart API:', error);
      Alertify.error(error.message);
      return Promise.reject(error);
    }
  },
);
/*---------------- Delete Cart ---------------- */
export const DeleteDealerCart = createAsyncThunk(
  'dealer-dealer-cart/dealer',
  async reqData => {
    try {
      const response = await deleteApi(DELETE_CART, reqData);
      return response;
    } catch (error) {
      console.log('Error Deleting Cart API:', error);
      Alertify.error(error.message);
      return Promise.reject(error);
    }
  },
);
/*---------------- Punch Order Request ---------------- */
export const DealerPunchOrderRequest = createAsyncThunk(
  'punch-order-request/dealer',
  async reqData => {
    try {
      const response = await postApi(DEALER_PUNCH_ORDER, reqData);
      return response;
    } catch (error) {
      console.log('Error Punch Order Request API:', error);
      Alertify.error(error.message);
      return Promise.reject(error);
    }
  },
);

/*---------------- Get Order Requests List ---------------- */
export const GetDealerOrderRequestsList = createAsyncThunk(
  'get-dealer-orders-list/dealer',
  async reqData => {
    try {
      const response = await postApi(GET_DEALER_ORDER_REQUESTS_LIST, reqData);
      return response;
    } catch (error) {
      console.log('Error Get Dealer Orders List API:', error);
      Alertify.error(error.message);
      return Promise.reject(error);
    }
  },
);

/*---------------- Get Orders List ---------------- */
export const GetDealerOrdersList = createAsyncThunk(
  'get-dealer-orders-list/dealer',
  async reqData => {
    try {
      const response = await postApi(GET_DEALER_ORDERS_LIST, reqData);
      return response;
    } catch (error) {
      console.log('Error Get Dealer Orders List API:', error);
      Alertify.error(error.message);
      return Promise.reject(error);
    }
  },
);
