import { createAsyncThunk } from '@reduxjs/toolkit';
import { postApi, handleApiError } from '../services/apiServices';
import {
  DEALER_FORGOT_PASSWORD,
  DEALER_LOGIN,
  DEALER_RESET_PASSWORD,
} from '../constants/apiConstants';
import Alertify from '../services/alertify';

/*----------------Login Dealer ---------------- */
export const LoginDealerData = createAsyncThunk(
  'login-dealer/dealer',
  async reqData => {
    try {
      console.log('Login Dealer Data:', reqData);
      const response = await postApi(DEALER_LOGIN, reqData);
      return response;
    } catch (error) {
      console.log('Error Log In API', error);
      Alertify.error(error.message);
      return Promise.reject(error);
    }
  },
);

/*---------------- Dealer Forgot Password ---------------- */
export const DealerForgotPwd = createAsyncThunk(
  'forgot-password-dealer/dealer',
  async reqData => {
    try {
      const response = await postApi(DEALER_FORGOT_PASSWORD, reqData);
      return response;
    } catch (error) {
      console.log('Error Forgot API', error);
      const errorMessage = error.error?.email?.[0] || 'An error occurred';
      Alertify.error('Forgot Password: ' + errorMessage);
      return Promise.reject(error);
    }
  },
);

/*---------------- Dealer Reset Password ---------------- */
export const DealerResetPwd = createAsyncThunk(
  'reset-password-dealer/daler',
  async reqData => {
    try {
      const response = await postApi(DEALER_RESET_PASSWORD, reqData);
      return response;
    } catch (error) {
      console.log('Reset Password API:', error);
      const errorMessage = error.error?.password?.[0] || 'An error occurred';
      Alertify.error(errorMessage);
      return Promise.reject(error);
    }
  },
);
