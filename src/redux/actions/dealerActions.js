import { createAsyncThunk } from '@reduxjs/toolkit';
import { postApi, handleApiError } from '../services/apiServices';
import { DEALER_DETAILS } from '../constants/apiConstants';

/*----------------Dealer Details ---------------- */
export const UserDealerDetails = createAsyncThunk(
  'dealer-details/user',
  async (reqData, { rejectWithValue }) => {
    try {
      const response = await postApi(DEALER_DETAILS, reqData);
      return response.data; // Extract data from response
    } catch (error) {
      console.log('Error Dealer Details API:', error);
      return handleApiError(error, rejectWithValue);
    }
  },
);
