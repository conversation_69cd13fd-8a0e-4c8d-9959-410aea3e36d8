import { createAsyncThunk } from '@reduxjs/toolkit';
import { postApi } from '../services/apiServices';
import { ALL_PRODUCTS } from '../constants/apiConstants';
import Alertify from '../services/alertify';

/*---------------- Get All Products ---------------- */
export const AllProductsAPI = createAsyncThunk(
  'all_products/admin',
  async reqData => {
    try {
      const { dealer_code } = reqData;
      const response = await postApi(ALL_PRODUCTS, { dealer_code });
      return response;
    } catch (error) {
      console.log('Error All ProductsAPI', error);
      Alertify.error(error.message);
      return Promise.reject(error);
    }
  },
);
