import { createAsyncThunk } from '@reduxjs/toolkit';
import { postApi } from '../services/apiServices';
import { ALL_PRODUCTS, PRODUCT_WITH_PRICE } from '../constants/apiConstants';
import Alertify from '../services/alertify';

/*---------------- Get All Products ---------------- */
export const AllProductsAPI = createAsyncThunk(
  'all_products/admin',
  async reqData => {
    try {
      const { dealer_code } = reqData;
      const response = await postApi(ALL_PRODUCTS, { dealer_code });
      return response;
    } catch (error) {
      console.log('Error All ProductsAPI', error);
      Alertify.error(error.message);
      return Promise.reject(error);
    }
  },
);
/*---------------- Product details with prices ---------------- */
export const PDetailsPrice = createAsyncThunk(
  'Product_details_with_price/admin',
  async reqData => {
    try {
      const response = await postApi(PRODUCT_WITH_PRICE, reqData);
      return response;
    } catch (error) {
      console.log('Error Product Details with Price API', error);
      Alertify.error(error.message);
      return Promise.reject(error);
    }
  },
);
