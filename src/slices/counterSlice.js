import { createSlice } from '@reduxjs/toolkit';

const counterSlice = createSlice({
  name: 'counter',
  initialState: { value: 0 },
  reducers: {
    increment: state => {
      state.value += 1;
    },
    addBy: (state, action) => {
      state.value += action.payload;
    },
    reset: state => {
      state.value = 0;
    },
  },
});

export const { increment, addBy, reset } = counterSlice.actions;
export default counterSlice.reducer;
