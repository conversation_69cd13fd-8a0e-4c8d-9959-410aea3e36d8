Stack trace:
Frame         Function      Args
0007FFFFAA90  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFAA90, 0007FFFF9990) msys-2.0.dll+0x2118E
0007FFFFAA90  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFAD68) msys-2.0.dll+0x69BA
0007FFFFAA90  0002100469F2 (00021028DF99, 0007FFFFA948, 0007FFFFAA90, 000000000000) msys-2.0.dll+0x69F2
0007FFFFAA90  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFAA90  00021006A545 (0007FFFFAAA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFAD70  00021006B9A5 (0007FFFFAAA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE345C0000 ntdll.dll
7FFE334D0000 KERNEL32.DLL
7FFE31A90000 KERNELBASE.dll
7FFE343B0000 USER32.dll
7FFE31FD0000 win32u.dll
000210040000 msys-2.0.dll
7FFE33EB0000 GDI32.dll
7FFE31750000 gdi32full.dll
7FFE31F20000 msvcp_win.dll
7FFE32240000 ucrtbase.dll
7FFE34260000 advapi32.dll
7FFE32490000 msvcrt.dll
7FFE33E00000 sechost.dll
7FFE330F0000 RPCRT4.dll
7FFE30D70000 CRYPTBASE.DLL
7FFE31880000 bcryptPrimitives.dll
7FFE33240000 IMM32.DLL
