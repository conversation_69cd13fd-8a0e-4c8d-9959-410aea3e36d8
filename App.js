// App.js
import React, { useEffect } from 'react';
import { AppState } from 'react-native';
import { Provider, useDispatch, useSelector } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { PersistGate } from 'redux-persist/integration/react';
import LoginScreen from './src/screens/login-screen';
import CartScreen from './src/screens/cart-screen';
import ShipmentDetailScreen from './src/screens/shipment-detail-screen';
import { checkSessionTimeout } from './src/redux/reducers/authSlice';
import OrderDetailsScreen from './src/screens/order-details-screen';
import ProfileScreen from './src/screens/profile-screen';
import store, { persistor } from './src/redux/store';

const Stack = createStackNavigator();

// --- App screens split into 2 stacks ---
function AuthStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="Login"
        component={LoginScreen}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
}

function MainStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="Cart"
        component={CartScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Shipment_Detail"
        component={ShipmentDetailScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Order_details"
        component={OrderDetailsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Profile_screen"
        component={ProfileScreen}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
}

// --- Gate that checks session and switches stacks ---
function AuthGate() {
  const dispatch = useDispatch();
  const isLogin = useSelector(state => state.auth_store.is_Login);
  const token = useSelector(state => state.auth_store.token);
  // const authData = useSelector(state => state.auth_store);
  // console.log('Auth Data:', authData);
  // Run timeout check on mount and when app becomes active
  useEffect(() => {
    dispatch(checkSessionTimeout()); // on mount

    const sub = AppState.addEventListener('change', state => {
      if (state === 'active') {
        dispatch(checkSessionTimeout()); // on resume
      }
    });

    return () => sub.remove();
  }, [dispatch]);

  // Render stacks conditionally; NavigationContainer will switch automatically
  return isLogin && token ? <MainStack /> : <AuthStack />;
}

export default function App() {
  console.log('App rendered');
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <NavigationContainer>
          <AuthGate />
        </NavigationContainer>
      </PersistGate>
    </Provider>
  );
}
